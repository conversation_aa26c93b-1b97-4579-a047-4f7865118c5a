package controller

import (
	"cve-mo3/models"
	"cve-mo3/services"
	"cve-mo3/response"
	"cve-mo3/response/fail"
	"github.com/gin-gonic/gin"
	"log"
	"net/http"
	"strconv"
)

// CVEStatusRequest CVE状态请求参数
type CVEStatusRequest struct {
	Status string `json:"status"`
}

func init() {
	// 注册路由
	cves := router.Api.Group("/cves")
	{
		cves.GET("", GetCVEs)
		cves.GET("/stats", GetCVEStats) // 静态路由必须在动态路由之前
		cves.GET("/:id", GetCVE)
		cves.PUT("/:id/status", UpdateCVEStatus)
		cves.DELETE("/:id", DeleteCVE)
	}
}

// CVEQueryParams CVE查询参数
type CVEQueryParams struct {
	Page       int    `form:"page" binding:"required,min=1"`
	PageSize   int    `form:"page_size" binding:"required,min=1,max=100"`
	Status     string `form:"status"`
	Search     string `form:"search"`
	Vendor     string `form:"vendor"`
	Product    string `form:"product"`
	MinCVSS    float64 `form:"min_cvss"`
}

// GetCVEs 获取CVE列表，支持分页和搜索
func GetCVEs(c *gin.Context) {
	var params CVEQueryParams
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusOK, fail.InvalidParams)
		return
	}

	service := services.NewCVEService()

	// 构建查询条件
	query := make(map[string]interface{})

	// 添加状态过滤
	if params.Status != "" {
		query["status"] = params.Status
	}

	// 添加厂商过滤
	if params.Vendor != "" {
		query["vendor"] = params.Vendor
	}

	// 添加产品过滤
	if params.Product != "" {
		query["product"] = params.Product
	}

	// 添加CVSS评分过滤
	if params.MinCVSS > 0 {
		query["min_cvss"] = params.MinCVSS
	}

	// 执行分页查询
	cves, total, err := service.GetPaged(params.Page, params.PageSize, query, params.Search)
	if err != nil {
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}

	// 构建响应数据
	responseData := gin.H{
		"list": cves,
		"pagination": gin.H{
			"current_page": params.Page,
			"page_size": params.PageSize,
			"total": total,
		},
	}

	c.JSON(http.StatusOK, response.Success(responseData, ""))
}

// GetCVE 获取单个CVE
func GetCVE(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusOK, fail.InvalidParams)
		return
	}

	service := services.NewCVEService()
	cve, err := service.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusOK, fail.CVENotFound)
		return
	}

	// 获取规则名称
	ruleNames, err := service.GetRuleNamesByIDs(cve.MatchedRules)
	if err != nil {
		log.Printf("获取规则名称失败: %v", err)
	}

	// 获取相关的GitHub仓库
	githubService := services.NewGitHubService()
	githubRepos, err := githubService.GetByCVEID(cve.CVEID)
	if err != nil {
		log.Printf("获取GitHub仓库失败: %v", err)
		// 如果获取GitHub仓库失败，设置为空数组，不影响整体响应
		githubRepos = []models.GitHubRepo{}
	}

	// 构建响应数据
	responseData := gin.H{
		"cve": cve,
		"rule_names": ruleNames,
		"github_repos": githubRepos,
	}

	c.JSON(http.StatusOK, response.Success(responseData, ""))
}

// GetCVEStats 获取CVE统计数据
func GetCVEStats(c *gin.Context) {
	service := services.NewCVEService()

	// 获取总数
	total, err := service.CountAll()
	if err != nil {
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}

	// 获取致命CVE数量
	critical, err := service.CountByCVSS(9.0)
	if err != nil {
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}

	// 获取关注中CVE数量
	watching, err := service.CountByStatus(string(models.CVEStatusWatching))
	if err != nil {
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}

	// 获取最近的CVE
	recentCVEs, err := service.GetRecent(5)
	if err != nil {
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}

	// 构建响应数据
	responseData := gin.H{
		"total": total,
		"critical": critical,
		"watching": watching,
		"recent": recentCVEs,
	}

	c.JSON(http.StatusOK, response.Success(responseData, ""))
}

// UpdateCVEStatus 更新CVE状态
func UpdateCVEStatus(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusOK, fail.InvalidParams)
		return
	}

	var req CVEStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, fail.InvalidParams)
		return
	}

	// 验证状态
	status := models.CVEStatus(req.Status)
	if status != models.CVEStatusWatching &&
	   status != models.CVEStatusToWatch &&
	   status != models.CVEStatusProcessed &&
	   status != models.CVEStatusIgnored {
		c.JSON(http.StatusOK, fail.InvalidParams)
		return
	}

	service := services.NewCVEService()
	if err := service.UpdateStatus(uint(id), status); err != nil {
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}

	c.JSON(http.StatusOK, response.Success(nil, "更新CVE状态成功"))
}

// DeleteCVE 删除CVE及其相关的GitHub仓库
func DeleteCVE(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusOK, fail.InvalidParams)
		return
	}

	service := services.NewCVEService()
	err = service.DeleteByID(uint(id))
	if err != nil {
		log.Printf("删除CVE失败: %v", err)
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}

	c.JSON(http.StatusOK, response.Success(nil, "CVE删除成功"))
}
