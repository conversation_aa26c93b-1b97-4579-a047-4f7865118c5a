package controller

import (
	"cve-mo3/models"
	"cve-mo3/response"
	"cve-mo3/response/fail"
	"cve-mo3/services"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
	"strings"
)

// ConfigRequest 配置请求参数
type ConfigRequest struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

func init() {
	// 注册路由
	config := router.Api.Group("/config")
	{
		config.GET("", GetConfigs)
		config.PUT("", UpdateConfig)
	}
}

// isValidCVSSLevels 验证CVSS级别配置是否有效
func isValidCVSSLevels(value string) bool {
	// 如果为空，则不过滤，有效
	if value == "" {
		return true
	}

	// 有效的CVSS级别
	validLevels := map[string]bool{
		"low":      true,
		"medium":   true,
		"high":     true,
		"critical": true,
	}

	// 分割并验证每个级别
	levels := strings.Split(value, "|")
	for _, level := range levels {
		level = strings.TrimSpace(level)
		if level == "" {
			continue
		}

		if !validLevels[level] {
			return false
		}
	}

	return true
}

// GetConfigs 获取所有配置
func GetConfigs(c *gin.Context) {
	service := services.NewConfigService()
	configs, err := service.GetAll()
	if err != nil {
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}

	c.JSON(http.StatusOK, response.Success(configs, ""))
}

// UpdateConfig 更新配置
func UpdateConfig(c *gin.Context) {
	var req ConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, fail.InvalidParams)
		return
	}

	// 验证键
	if req.Key == "" {
		c.JSON(http.StatusOK, fail.InvalidParams)
		return
	}

	// 如果是CVSS级别配置，验证值是否有效
	if req.Key == string(models.ConfigCVSSLevels) {
		if !isValidCVSSLevels(req.Value) {
			c.JSON(http.StatusOK, response.NewResponse(400, "无效的CVSS级别配置，有效值为: low, medium, high, critical，多个级别用|分隔", nil))
			return
		}
	}

	// 如果是CVE最大年龄配置，验证值是否有效
	if req.Key == string(models.ConfigCVEMaxAge) {
		age, err := strconv.Atoi(req.Value)
		if err != nil || age < 0 {
			c.JSON(http.StatusOK, response.NewResponse(400, "无效的CVE最大年龄配置，必须是非负整数", nil))
			return
		}
	}

	service := services.NewConfigService()
	if err := service.UpdateValue(models.ConfigKey(req.Key), req.Value); err != nil {
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}

	// 如果更新的是监控相关的配置，则更新调度器
	switch models.ConfigKey(req.Key) {
	case models.ConfigCVEMonitorInterval:
		if err := services.UpdateCVESchedule(); err != nil {
			c.JSON(http.StatusOK, response.NewResponse(200, "配置已更新，但调度器更新失败", nil))
			return
		}
	case models.ConfigGitHubMonitorInterval, models.ConfigGitHubMonitorEnabled:
		if err := services.UpdateGitHubSchedule(); err != nil {
			c.JSON(http.StatusOK, response.NewResponse(200, "配置已更新，但调度器更新失败", nil))
			return
		}
	}

	c.JSON(http.StatusOK, response.Success(nil, "更新配置成功"))
}
