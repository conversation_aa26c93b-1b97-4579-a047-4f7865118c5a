package controller

import (
	"cve-mo3/services"
	"cve-mo3/response"
	"cve-mo3/response/fail"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
)



// GitHubQueryParams GitHub仓库查询参数
type GitHubQueryParams struct {
	Page       int    `form:"page" binding:"required,min=1"`
	PageSize   int    `form:"page_size" binding:"required,min=1,max=100"`
	Search     string `form:"search"`
	MinStars   int    `form:"min_stars"`
	CVEID      string `form:"cve_id"`
}

// GetGitHubRepos 获取GitHub仓库列表，支持分页和搜索
func GetGitHubRepos(c *gin.Context) {
	var params GitHubQueryParams
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusOK, fail.InvalidParams)
		return
	}

	service := services.NewGitHubService()

	// 构建查询条件
	query := make(map[string]interface{})

	// 添加最小星数过滤
	if params.MinStars > 0 {
		query["min_stars"] = params.MinStars
	}

	// 添加CVE ID过滤
	if params.CVEID != "" {
		query["cve_id"] = params.CVEID
	}

	// 执行分页查询
	repos, total, err := service.GetPaged(params.Page, params.PageSize, query, params.Search)
	if err != nil {
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}

	// 构建响应数据
	responseData := gin.H{
		"list": repos,
		"pagination": gin.H{
			"current_page": params.Page,
			"page_size": params.PageSize,
			"total": total,
		},
	}

	c.JSON(http.StatusOK, response.Success(responseData, ""))
}

// GetGitHubRepo 获取单个GitHub仓库
func GetGitHubRepo(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusOK, fail.InvalidParams)
		return
	}

	service := services.NewGitHubService()
	repo, err := service.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusOK, fail.NotFound)
		return
	}

	c.JSON(http.StatusOK, response.Success(repo, ""))
}

// GetGitHubReposByCVE 根据CVE ID获取GitHub仓库
func GetGitHubReposByCVE(c *gin.Context) {
	cveID := c.Param("cveId")
	if cveID == "" {
		c.JSON(http.StatusOK, fail.InvalidParams)
		return
	}

	service := services.NewGitHubService()
	repos, err := service.GetByCVEID(cveID)
	if err != nil {
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}

	c.JSON(http.StatusOK, response.Success(repos, ""))
}
