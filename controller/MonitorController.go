package controller

import (
	"cve-mo3/models"
	"cve-mo3/services"
	"cve-mo3/response"
	"cve-mo3/response/fail"
	"github.com/gin-gonic/gin"
	"net/http"
)



// GetMonitorHistory 获取监控历史
func GetMonitorHistory(c *gin.Context) {
	service := services.NewMonitorHistoryService()

	// 检查是否有类型过滤
	monitorType := c.Query("type")

	var histories []models.MonitorHistory
	var err error

	if monitorType != "" {
		histories, err = service.GetByType(models.MonitorType(monitorType))
	} else {
		histories, err = service.GetAll()
	}

	if err != nil {
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}

	c.JSON(http.StatusOK, response.Success(histories, ""))
}

// RunCVEMonitor 运行CVE监控
func RunCVEMonitor(c *gin.Context) {
	service := services.NewMonitorService()
	// 使用手动触发方法，绕过时间间隔检查
	if err := service.RunCVEMonitorManually(); err != nil {
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}

	c.JSON(http.StatusOK, response.Success(nil, "CVE监控运行成功"))
}

// RunGitHubMonitor 运行GitHub监控
func RunGitHubMonitor(c *gin.Context) {
	service := services.NewMonitorService()
	// 使用手动触发方法，绕过时间间隔检查
	if err := service.RunGitHubMonitorManually(); err != nil {
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}

	c.JSON(http.StatusOK, response.Success(nil, "GitHub监控运行成功"))
}
