package controller

import (
	"cve-mo3/models"
	"cve-mo3/services"
	"cve-mo3/response"
	"cve-mo3/response/fail"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
)

// RuleRequest 规则请求参数
type RuleRequest struct {
	Name         string  `json:"name" binding:"required"` // 规则名称，必填
	Keywords     string  `json:"keywords"`
	VendorName   string  `json:"vendor_name"`
	ProductName  string  `json:"product_name"`
	MinCVSSScore float64 `json:"min_cvss_score"`
	Enabled      bool    `json:"enabled"`
}

// RuleQueryParams 规则查询参数
type RuleQueryParams struct {
	Page     int    `form:"page" binding:"min=1"`
	PageSize int    `form:"page_size" binding:"min=1,max=100"`
	Search   string `form:"search"`
}

func init() {
	// 注册路由
	rules := router.Api.Group("/rules")
	{
		rules.GET("", GetRules)
		rules.GET("/:id", GetRule)
		rules.POST("", CreateRule)
		rules.PUT("/:id", UpdateRule)
		rules.DELETE("/:id", DeleteRule)
	}
}

// GetRules 获取规则列表，支持分页和搜索
func GetRules(c *gin.Context) {
	var params RuleQueryParams
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusOK, fail.InvalidParams)
		return
	}

	// 设置默认值
	if params.Page == 0 {
		params.Page = 1
	}
	if params.PageSize == 0 {
		params.PageSize = 10
	}

	service := services.NewRuleService()

	// 执行分页查询（带统计）
	rules, total, err := service.GetPagedWithStats(params.Page, params.PageSize, params.Search)
	if err != nil {
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}

	// 计算总页数
	totalPages := (total + int64(params.PageSize) - 1) / int64(params.PageSize)

	// 构建响应数据
	responseData := gin.H{
		"data": rules,
		"pagination": gin.H{
			"page":        params.Page,
			"page_size":   params.PageSize,
			"total":       total,
			"total_pages": totalPages,
		},
	}

	c.JSON(http.StatusOK, response.Success(responseData, ""))
}

// GetRule 获取单个规则
func GetRule(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusOK, fail.InvalidParams)
		return
	}

	service := services.NewRuleService()
	rule, err := service.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusOK, fail.RuleNotFound)
		return
	}

	c.JSON(http.StatusOK, response.Success(rule, ""))
}

// CreateRule 创建规则
func CreateRule(c *gin.Context) {
	var req RuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, fail.InvalidParams)
		return
	}

	// 检查规则名称是否已存在
	service := services.NewRuleService()
	exists, err := service.ExistsByName(req.Name)
	if err != nil {
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}
	if exists {
		c.JSON(http.StatusOK, response.Fail("规则名称已存在"))
		return
	}

	rule := models.Rule{
		Name:         req.Name,
		Keywords:     req.Keywords,
		VendorName:   req.VendorName,
		ProductName:  req.ProductName,
		MinCVSSScore: req.MinCVSSScore,
		Enabled:      req.Enabled,
	}

	if err := service.Create(&rule); err != nil {
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}

	c.JSON(http.StatusOK, response.Success(rule, "创建规则成功"))
}

// UpdateRule 更新规则
func UpdateRule(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusOK, fail.InvalidParams)
		return
	}

	var req RuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, fail.InvalidParams)
		return
	}

	service := services.NewRuleService()
	rule, err := service.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusOK, fail.RuleNotFound)
		return
	}

	// 如果名称发生变化，检查新名称是否已存在
	if rule.Name != req.Name {
		exists, err := service.ExistsByName(req.Name)
		if err != nil {
			c.JSON(http.StatusOK, fail.ServerError)
			return
		}
		if exists {
			c.JSON(http.StatusOK, response.Fail("规则名称已存在"))
			return
		}
	}

	rule.Name = req.Name
	rule.Keywords = req.Keywords
	rule.VendorName = req.VendorName
	rule.ProductName = req.ProductName
	rule.MinCVSSScore = req.MinCVSSScore
	rule.Enabled = req.Enabled

	if err := service.Update(&rule); err != nil {
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}

	c.JSON(http.StatusOK, response.Success(rule, "更新规则成功"))
}

// DeleteRule 删除规则
func DeleteRule(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusOK, fail.InvalidParams)
		return
	}

	service := services.NewRuleService()
	if err := service.Delete(uint(id)); err != nil {
		c.JSON(http.StatusOK, fail.ServerError)
		return
	}

	c.JSON(http.StatusOK, response.Success(nil, "删除规则成功"))
}
