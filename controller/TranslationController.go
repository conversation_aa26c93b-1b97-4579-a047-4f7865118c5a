package controller

import (
	"cve-mo3/response"
	"cve-mo3/services"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)



// TranslateCVE 翻译CVE
func TranslateCVE(c *gin.Context) {
	// 获取CVE ID
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusOK, response.NewResponse(400, "无效的CVE ID", nil))
		return
	}

	// 创建翻译服务
	translationService := services.NewTranslationService()

	// 检查翻译功能是否开启
	enabled, err := translationService.IsTranslationEnabled()
	if err != nil {
		c.JSON(http.StatusOK, response.NewResponse(500, "检查翻译功能状态失败: "+err.Error(), nil))
		return
	}

	if !enabled {
		c.JSON(http.StatusOK, response.NewResponse(400, "翻译功能未开启，请在系统设置中开启 DeepSeek 翻译功能", nil))
		return
	}

	// 检查API密钥是否配置
	apiKey, err := translationService.GetDeepSeekAPIKey()
	if err != nil {
		c.JSON(http.StatusOK, response.NewResponse(500, "获取DeepSeek API密钥失败: "+err.Error(), nil))
		return
	}

	if apiKey == "" {
		c.JSON(http.StatusOK, response.NewResponse(400, "DeepSeek API密钥未配置，请在系统设置中配置", nil))
		return
	}

	// 翻译CVE
	if err := translationService.TranslateExistingCVE(uint(id)); err != nil {
		c.JSON(http.StatusOK, response.NewResponse(500, "翻译CVE失败: "+err.Error(), nil))
		return
	}

	c.JSON(http.StatusOK, response.Success(nil, "翻译成功"))
}
