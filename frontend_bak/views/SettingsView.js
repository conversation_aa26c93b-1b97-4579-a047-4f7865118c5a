import Card from '../components/common/Card.js';
import configService from '../services/configService.js';

export default {
  components: {
    Card
  },
  setup() {
    const { ref, reactive, onMounted } = Vue;
    
    // 状态
    const configs = ref([]);
    const loading = ref(true);
    const saving = ref(false);
    const editingConfig = ref(null);
    
    // 表单数据
    const configForm = reactive({
      key: '',
      value: '',
      description: ''
    });
    
    // 方法
    const loadConfigs = async () => {
      loading.value = true;
      try {
        const response = await configService.getAllConfigs();
        if (response.data) {
          configs.value = response.data;
        }
      } catch (error) {
        console.error('加载配置数据失败:', error);
      } finally {
        loading.value = false;
      }
    };
    
    const updateConfig = async (config) => {
      saving.value = true;
      try {
        await configService.updateConfig(config.key, config.value);
        
        // 更新本地数据
        const index = configs.value.findIndex(c => c.key === config.key);
        if (index !== -1) {
          configs.value[index].value = config.value;
        }
        
        alert('配置已更新');
      } catch (error) {
        console.error('更新配置失败:', error);
        alert('更新配置失败: ' + error.message);
      } finally {
        saving.value = false;
        editingConfig.value = null;
      }
    };
    
    const editConfig = (config) => {
      editingConfig.value = { ...config };
    };
    
    const cancelEdit = () => {
      editingConfig.value = null;
    };
    
    // 生命周期钩子
    onMounted(() => {
      loadConfigs();
    });
    
    return {
      configs,
      loading,
      saving,
      editingConfig,
      configForm,
      loadConfigs,
      updateConfig,
      editConfig,
      cancelEdit
    };
  },
  template: `
    <div>
      <div class="mb-6 flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-800">系统设置</h1>
        <button @click="loadConfigs" class="btn-primary flex items-center">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          刷新配置
        </button>
      </div>
      
      <!-- Config Categories -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- API Settings -->
        <Card title="API 设置" subtitle="外部API认证信息">
          <div v-if="loading" class="py-4 text-center text-gray-500">
            加载中...
          </div>
          <div v-else class="space-y-4">
            <!-- OpenCVE Username -->
            <div>
              <div class="flex justify-between items-center mb-1">
                <label class="text-sm font-medium text-gray-700">OpenCVE 用户名</label>
                <button 
                  v-if="editingConfig && editingConfig.key === 'opencve_username'"
                  @click="cancelEdit"
                  class="text-xs text-gray-500 hover:text-gray-700"
                >
                  取消
                </button>
                <button 
                  v-else
                  @click="editConfig(configs.find(c => c.key === 'opencve_username'))"
                  class="text-xs text-blue-600 hover:text-blue-800"
                >
                  编辑
                </button>
              </div>
              
              <div v-if="editingConfig && editingConfig.key === 'opencve_username'" class="flex space-x-2">
                <input 
                  v-model="editingConfig.value" 
                  type="text" 
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                <button 
                  @click="updateConfig(editingConfig)" 
                  class="btn-primary"
                  :disabled="saving"
                >
                  保存
                </button>
              </div>
              <div v-else class="text-sm text-gray-600">
                {{ configs.find(c => c.key === 'opencve_username')?.value || '未设置' }}
              </div>
              <p class="mt-1 text-xs text-gray-500">
                OpenCVE API 的用户名
              </p>
            </div>
            
            <!-- OpenCVE Password -->
            <div>
              <div class="flex justify-between items-center mb-1">
                <label class="text-sm font-medium text-gray-700">OpenCVE 密码</label>
                <button 
                  v-if="editingConfig && editingConfig.key === 'opencve_password'"
                  @click="cancelEdit"
                  class="text-xs text-gray-500 hover:text-gray-700"
                >
                  取消
                </button>
                <button 
                  v-else
                  @click="editConfig(configs.find(c => c.key === 'opencve_password'))"
                  class="text-xs text-blue-600 hover:text-blue-800"
                >
                  编辑
                </button>
              </div>
              
              <div v-if="editingConfig && editingConfig.key === 'opencve_password'" class="flex space-x-2">
                <input 
                  v-model="editingConfig.value" 
                  type="password" 
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                <button 
                  @click="updateConfig(editingConfig)" 
                  class="btn-primary"
                  :disabled="saving"
                >
                  保存
                </button>
              </div>
              <div v-else class="text-sm text-gray-600">
                {{ configs.find(c => c.key === 'opencve_password')?.value ? '******' : '未设置' }}
              </div>
              <p class="mt-1 text-xs text-gray-500">
                OpenCVE API 的密码
              </p>
            </div>
            
            <!-- GitHub Token -->
            <div>
              <div class="flex justify-between items-center mb-1">
                <label class="text-sm font-medium text-gray-700">GitHub Token</label>
                <button 
                  v-if="editingConfig && editingConfig.key === 'github_token'"
                  @click="cancelEdit"
                  class="text-xs text-gray-500 hover:text-gray-700"
                >
                  取消
                </button>
                <button 
                  v-else
                  @click="editConfig(configs.find(c => c.key === 'github_token'))"
                  class="text-xs text-blue-600 hover:text-blue-800"
                >
                  编辑
                </button>
              </div>
              
              <div v-if="editingConfig && editingConfig.key === 'github_token'" class="flex space-x-2">
                <input 
                  v-model="editingConfig.value" 
                  type="password" 
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                <button 
                  @click="updateConfig(editingConfig)" 
                  class="btn-primary"
                  :disabled="saving"
                >
                  保存
                </button>
              </div>
              <div v-else class="text-sm text-gray-600">
                {{ configs.find(c => c.key === 'github_token')?.value ? '******' : '未设置' }}
              </div>
              <p class="mt-1 text-xs text-gray-500">
                GitHub API 的访问令牌
              </p>
            </div>
            
            <!-- Feishu Webhook URL -->
            <div>
              <div class="flex justify-between items-center mb-1">
                <label class="text-sm font-medium text-gray-700">飞书 Webhook URL</label>
                <button 
                  v-if="editingConfig && editingConfig.key === 'feishu_webhook_url'"
                  @click="cancelEdit"
                  class="text-xs text-gray-500 hover:text-gray-700"
                >
                  取消
                </button>
                <button 
                  v-else
                  @click="editConfig(configs.find(c => c.key === 'feishu_webhook_url'))"
                  class="text-xs text-blue-600 hover:text-blue-800"
                >
                  编辑
                </button>
              </div>
              
              <div v-if="editingConfig && editingConfig.key === 'feishu_webhook_url'" class="flex space-x-2">
                <input 
                  v-model="editingConfig.value" 
                  type="text" 
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                <button 
                  @click="updateConfig(editingConfig)" 
                  class="btn-primary"
                  :disabled="saving"
                >
                  保存
                </button>
              </div>
              <div v-else class="text-sm text-gray-600 truncate">
                {{ configs.find(c => c.key === 'feishu_webhook_url')?.value || '未设置' }}
              </div>
              <p class="mt-1 text-xs text-gray-500">
                飞书机器人的 Webhook URL
              </p>
            </div>
          </div>
        </Card>
        
        <!-- Monitor Settings -->
        <Card title="监控设置" subtitle="监控任务配置">
          <div v-if="loading" class="py-4 text-center text-gray-500">
            加载中...
          </div>
          <div v-else class="space-y-4">
            <!-- CVE Monitor Interval -->
            <div>
              <div class="flex justify-between items-center mb-1">
                <label class="text-sm font-medium text-gray-700">CVE 监控间隔（分钟）</label>
                <button 
                  v-if="editingConfig && editingConfig.key === 'cve_monitor_interval'"
                  @click="cancelEdit"
                  class="text-xs text-gray-500 hover:text-gray-700"
                >
                  取消
                </button>
                <button 
                  v-else
                  @click="editConfig(configs.find(c => c.key === 'cve_monitor_interval'))"
                  class="text-xs text-blue-600 hover:text-blue-800"
                >
                  编辑
                </button>
              </div>
              
              <div v-if="editingConfig && editingConfig.key === 'cve_monitor_interval'" class="flex space-x-2">
                <input 
                  v-model="editingConfig.value" 
                  type="number" 
                  min="1" 
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                <button 
                  @click="updateConfig(editingConfig)" 
                  class="btn-primary"
                  :disabled="saving"
                >
                  保存
                </button>
              </div>
              <div v-else class="text-sm text-gray-600">
                {{ configs.find(c => c.key === 'cve_monitor_interval')?.value || '60' }} 分钟
              </div>
              <p class="mt-1 text-xs text-gray-500">
                CVE 监控任务的执行间隔时间
              </p>
            </div>
            
            <!-- GitHub Monitor Interval -->
            <div>
              <div class="flex justify-between items-center mb-1">
                <label class="text-sm font-medium text-gray-700">GitHub 监控间隔（分钟）</label>
                <button 
                  v-if="editingConfig && editingConfig.key === 'github_monitor_interval'"
                  @click="cancelEdit"
                  class="text-xs text-gray-500 hover:text-gray-700"
                >
                  取消
                </button>
                <button 
                  v-else
                  @click="editConfig(configs.find(c => c.key === 'github_monitor_interval'))"
                  class="text-xs text-blue-600 hover:text-blue-800"
                >
                  编辑
                </button>
              </div>
              
              <div v-if="editingConfig && editingConfig.key === 'github_monitor_interval'" class="flex space-x-2">
                <input 
                  v-model="editingConfig.value" 
                  type="number" 
                  min="1" 
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                <button 
                  @click="updateConfig(editingConfig)" 
                  class="btn-primary"
                  :disabled="saving"
                >
                  保存
                </button>
              </div>
              <div v-else class="text-sm text-gray-600">
                {{ configs.find(c => c.key === 'github_monitor_interval')?.value || '120' }} 分钟
              </div>
              <p class="mt-1 text-xs text-gray-500">
                GitHub 监控任务的执行间隔时间
              </p>
            </div>
            
            <!-- GitHub Monitor Enabled -->
            <div>
              <div class="flex justify-between items-center mb-1">
                <label class="text-sm font-medium text-gray-700">启用 GitHub 监控</label>
                <button 
                  v-if="editingConfig && editingConfig.key === 'github_monitor_enabled'"
                  @click="cancelEdit"
                  class="text-xs text-gray-500 hover:text-gray-700"
                >
                  取消
                </button>
                <button 
                  v-else
                  @click="editConfig(configs.find(c => c.key === 'github_monitor_enabled'))"
                  class="text-xs text-blue-600 hover:text-blue-800"
                >
                  编辑
                </button>
              </div>
              
              <div v-if="editingConfig && editingConfig.key === 'github_monitor_enabled'" class="flex space-x-2">
                <select 
                  v-model="editingConfig.value" 
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="true">是</option>
                  <option value="false">否</option>
                </select>
                <button 
                  @click="updateConfig(editingConfig)" 
                  class="btn-primary"
                  :disabled="saving"
                >
                  保存
                </button>
              </div>
              <div v-else class="text-sm text-gray-600">
                {{ configs.find(c => c.key === 'github_monitor_enabled')?.value === 'true' ? '是' : '否' }}
              </div>
              <p class="mt-1 text-xs text-gray-500">
                是否启用 GitHub 监控任务
              </p>
            </div>
            
            <!-- CVSS Levels -->
            <div>
              <div class="flex justify-between items-center mb-1">
                <label class="text-sm font-medium text-gray-700">CVSS 级别</label>
                <button 
                  v-if="editingConfig && editingConfig.key === 'cvss_levels'"
                  @click="cancelEdit"
                  class="text-xs text-gray-500 hover:text-gray-700"
                >
                  取消
                </button>
                <button 
                  v-else
                  @click="editConfig(configs.find(c => c.key === 'cvss_levels'))"
                  class="text-xs text-blue-600 hover:text-blue-800"
                >
                  编辑
                </button>
              </div>
              
              <div v-if="editingConfig && editingConfig.key === 'cvss_levels'" class="flex space-x-2">
                <select 
                  v-model="editingConfig.value" 
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  multiple
                >
                  <option value="low">低危 (Low)</option>
                  <option value="medium">中危 (Medium)</option>
                  <option value="high">高危 (High)</option>
                  <option value="critical">严重 (Critical)</option>
                </select>
                <button 
                  @click="updateConfig({...editingConfig, value: editingConfig.value.join('|')})" 
                  class="btn-primary"
                  :disabled="saving"
                >
                  保存
                </button>
              </div>
              <div v-else class="text-sm text-gray-600">
                <div class="flex flex-wrap gap-2">
                  <span 
                    v-for="level in (configs.find(c => c.key === 'cvss_levels')?.value || 'high|critical').split('|')" 
                    :key="level"
                    class="px-2 py-1 text-xs rounded-full"
                    :class="{
                      'bg-red-100 text-red-800': level === 'critical',
                      'bg-orange-100 text-orange-800': level === 'high',
                      'bg-yellow-100 text-yellow-800': level === 'medium',
                      'bg-green-100 text-green-800': level === 'low'
                    }"
                  >
                    {{ level === 'critical' ? '严重' : level === 'high' ? '高危' : level === 'medium' ? '中危' : '低危' }}
                  </span>
                </div>
              </div>
              <p class="mt-1 text-xs text-gray-500">
                要监控的 CVSS 级别，多个级别用 | 分隔
              </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  `
};