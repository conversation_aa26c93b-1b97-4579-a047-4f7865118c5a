package main

import (
	"fmt"
	"log"
	"time"

	"cve-mo3/models"
	"cve-mo3/router"
	"cve-mo3/services"

	"github.com/go-co-op/gocron"
)

func main() {
	// 使用默认配置
	dbPath := "cve-mo3.db"
	port := 15656

	// 初始化数据库
	if err := services.InitDB(dbPath); err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}

	// 初始化全局调度器
	services.Scheduler = gocron.NewScheduler(time.UTC)

	// 设置调度器
	services.CVEJob, services.GitHubJob = setupScheduler(services.Scheduler)

	// 启动调度器
	services.Scheduler.StartAsync()

	// 初始化路由
	router.Init()

	// 启动服务器
	addr := fmt.Sprintf(":%d", port)
	log.Printf("服务器已启动，监听端口 %s", addr)
	if err := router.Router.Run(addr); err != nil {
		log.Fatalf("启动服务器失败: %v", err)
	}
}

// setupScheduler 设置定期任务的调度器
func setupScheduler(scheduler *gocron.Scheduler) (cveJob, githubJob *gocron.Job) {
	configService := services.NewConfigService()
	monitorService := services.NewMonitorService()

	// 调度CVE监控
	cveInterval, err := configService.GetIntValue(models.ConfigCVEMonitorInterval)
	if err != nil {
		log.Printf("获取CVE监控间隔失败: %v", err)
		cveInterval = 60 // 默认为60分钟
	}

	cveJob, err = scheduler.Every(cveInterval).Minutes().Do(func() {
		log.Println("运行CVE监控...")
		if err := monitorService.RunCVEMonitor(); err != nil {
			log.Printf("运行CVE监控失败: %v", err)
		}
	})
	if err != nil {
		log.Printf("设置CVE监控任务失败: %v", err)
		cveJob = nil
	}

	// 调度GitHub监控
	githubEnabled, err := configService.GetBoolValue(models.ConfigGitHubMonitorEnabled)
	if err != nil {
		log.Printf("获取GitHub监控启用状态失败: %v", err)
		githubEnabled = true // 默认为启用
	}

	if githubEnabled {
		githubInterval, err := configService.GetIntValue(models.ConfigGitHubMonitorInterval)
		if err != nil {
			log.Printf("获取GitHub监控间隔失败: %v", err)
			githubInterval = 120 // 默认为120分钟
		}

		githubJob, err = scheduler.Every(githubInterval).Minutes().Do(func() {
			log.Println("运行GitHub监控...")
			if err := monitorService.RunGitHubMonitor(); err != nil {
				log.Printf("运行GitHub监控失败: %v", err)
			}
		})
		if err != nil {
			log.Printf("设置GitHub监控任务失败: %v", err)
			githubJob = nil
		}
	}

	// 记录调度的任务
	for _, job := range scheduler.Jobs() {
		log.Printf("计划任务: %s", job.ScheduledAtTime())
	}

	return
}
