package middleware

import (
	"cve-mo3/models"
	"cve-mo3/utils"
	"net/http"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// JWTAuth JWT认证中间件
func JWTAuth(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从自定义请求头获取token
		tokenString := c.<PERSON>er("X-Auth-Token")
		if tokenString == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "未提供认证token",
			})
			c.Abort()
			return
		}

		// 解析token
		claims, err := utils.ParseToken(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "token无效",
			})
			c.Abort()
			return
		}

		// 验证用户是否存在
		var user models.User
		if err := db.First(&user, claims.UserID).Error; err != nil {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "用户不存在",
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("user", user)

		c.Next()
	}
}

// SiteAuth401 站点401认证中间件（全站保护）
func SiteAuth401(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否启用401认证
		var setting models.Config
		if err := db.Where("key = ?", "auth_401_enabled").First(&setting).Error; err != nil || setting.Value != "true" {
			// 401认证未启用，直接通过
			c.Next()
			return
		}

		// 获取401认证的用户名和密码
		var usernameSetting, passwordSetting models.Config
		if err := db.Where("key = ?", "auth_401_username").First(&usernameSetting).Error; err != nil {
			c.Header("WWW-Authenticate", `Basic realm="Protected Site"`)
			c.String(http.StatusUnauthorized, "401 Unauthorized")
			c.Abort()
			return
		}
		if err := db.Where("key = ?", "auth_401_password").First(&passwordSetting).Error; err != nil {
			c.Header("WWW-Authenticate", `Basic realm="Protected Site"`)
			c.String(http.StatusUnauthorized, "401 Unauthorized")
			c.Abort()
			return
		}

		// 获取Basic Auth信息
		username, password, hasAuth := c.Request.BasicAuth()
		if !hasAuth {
			c.Header("WWW-Authenticate", `Basic realm="Protected Site"`)
			c.String(http.StatusUnauthorized, "401 Unauthorized")
			c.Abort()
			return
		}

		// 验证用户名和密码
		if username != usernameSetting.Value || password != passwordSetting.Value {
			c.Header("WWW-Authenticate", `Basic realm="Protected Site"`)
			c.String(http.StatusUnauthorized, "401 Unauthorized")
			c.Abort()
			return
		}

		c.Next()
	}
}
