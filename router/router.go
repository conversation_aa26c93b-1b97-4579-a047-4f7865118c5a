package router

import (
	"cve-mo3/controller"
	"cve-mo3/middleware"
	"cve-mo3/services"
	"os"
	"strings"

	"github.com/gin-gonic/gin"
)

// 全局路由变量
var (
	Router = gin.Default()
	Api    = Router.Group("/api")
)

// 初始化路由
func Init() {

	// 添加CORS中间件
	Router.Use(func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 设置认证路由
	setupAuthRoutes()

	// 设置API路由（需要认证）
	setupAPIRoutes()

	// 提供前端静态文件
	// 使用embed包提供前端静态文件服务
	ServeStaticFiles()
}

// ServeStaticFiles 提供前端静态文件服务
func ServeStaticFiles() {
	// 静态资源目录
	Router.Static("/js", "./static/js")

	// 处理所有前端路由
	Router.NoRoute(func(c *gin.Context) {
		if strings.HasPrefix(c.Request.URL.Path, "/api") {
			// 交由后续处理
			c.Next()
		} else {
			// 静态文件尝试访问
			requestPath := c.Request.URL.Path
			fullPath := "./static" + requestPath
			if _, err := os.Stat(fullPath); err == nil {
				c.File(fullPath)
				c.Abort()
				return
			}

			// 如果文件不存在，则返回 index.html（前端路由）
			c.File("./static/index.html")
			c.Abort()
		}
	})

	// 登录页面路由
	Router.GET("/login", func(c *gin.Context) {
		c.File("./static/login.html")
	})

	// 根路径重定向到前端入口
	Router.GET("/", func(c *gin.Context) {
		c.File("./static/index.html")
	})
}

// setupAuthRoutes 设置认证相关路由（不需要认证）
func setupAuthRoutes() {
	auth := Api.Group("/auth")
	{
		auth.POST("/login", controller.Login)
		auth.POST("/logout", controller.Logout)
		auth.POST("/refresh", controller.RefreshToken)
	}
}

// setupAPIRoutes 设置需要认证的API路由
func setupAPIRoutes() {
	// 应用401认证中间件（如果启用）
	Api.Use(middleware.BasicAuth401(services.DB))

	// 应用JWT认证中间件
	Api.Use(middleware.JWTAuth(services.DB))

	// 用户相关路由
	user := Api.Group("/user")
	{
		user.GET("/profile", controller.GetProfile)
		user.POST("/change-password", controller.ChangePassword)
	}

	// CVE相关路由
	cves := Api.Group("/cves")
	{
		cves.GET("", controller.GetCVEs)
		cves.GET("/stats", controller.GetCVEStats) // 静态路由必须在动态路由之前
		cves.GET("/:id", controller.GetCVE)
		cves.PUT("/:id/status", controller.UpdateCVEStatus)
		cves.DELETE("/:id", controller.DeleteCVE)
	}

	// 规则相关路由
	rules := Api.Group("/rules")
	{
		rules.GET("", controller.GetRules)
		rules.GET("/:id", controller.GetRule)
		rules.POST("", controller.CreateRule)
		rules.PUT("/:id", controller.UpdateRule)
		rules.DELETE("/:id", controller.DeleteRule)
	}

	// 监控相关路由
	monitor := Api.Group("/monitor")
	{
		monitor.GET("/history", controller.GetMonitorHistory)
		monitor.POST("/cve", controller.RunCVEMonitor)
		monitor.POST("/github", controller.RunGitHubMonitor)
	}

	// GitHub相关路由
	github := Api.Group("/github")
	{
		github.GET("", controller.GetGitHubRepos)
		github.GET("/:id", controller.GetGitHubRepo)
		github.GET("/cve/:cveId", controller.GetGitHubReposByCVE)
	}

	// 翻译相关路由
	translation := Api.Group("/translation")
	{
		translation.POST("/cve/:id", controller.TranslateCVE)
	}

	// 配置相关路由
	config := Api.Group("/config")
	{
		config.GET("", controller.GetConfigs)
		config.PUT("", controller.UpdateConfig)
	}
}
