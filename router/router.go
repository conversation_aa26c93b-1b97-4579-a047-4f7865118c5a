package router

import (
	"cve-mo3/controller"
	"cve-mo3/middleware"
	"cve-mo3/services"
	"os"
	"strings"

	"github.com/gin-gonic/gin"
)

// 全局路由变量
var (
	Router = gin.Default()
	Api    = Router.Group("/api")
)

// 初始化路由
func Init() {

	// 添加CORS中间件
	Router.Use(func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 提供前端静态文件
	// 使用embed包提供前端静态文件服务
	ServeStaticFiles()
}

// ServeStaticFiles 提供前端静态文件服务
func ServeStaticFiles() {
	// 静态资源目录
	Router.Static("/js", "./static/js")

	// 处理所有前端路由
	Router.NoRoute(func(c *gin.Context) {
		if strings.HasPrefix(c.Request.URL.Path, "/api") {
			// 交由后续处理
			c.Next()
		} else {
			// 静态文件尝试访问
			requestPath := c.Request.URL.Path
			fullPath := "./static" + requestPath
			if _, err := os.Stat(fullPath); err == nil {
				c.File(fullPath)
				c.Abort()
				return
			}

			// 如果文件不存在，则返回 index.html（前端路由）
			c.File("./static/index.html")
			c.Abort()
		}
	})

	// 根路径重定向到前端入口
	Router.GET("/", func(c *gin.Context) {
		c.File("./static/index.html")
	})
}
