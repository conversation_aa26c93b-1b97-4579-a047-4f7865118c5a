package services

import (
	"log"
	"time"

	"cve-mo3/models"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

// InitDB 初始化数据库连接
func InitDB(dbPath string) error {
	var err error

	// 配置GORM日志记录器
	newLogger := logger.New(
		log.New(log.Writer(), "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  true,
		},
	)

	// 连接到SQLite数据库
	DB, err = gorm.Open(sqlite.Open(dbPath), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		return err
	}

	// 自动迁移数据库结构
	err = DB.AutoMigrate(
		&models.Rule{},
		&models.CVE{},
		&models.GitHubRepo{},
		&models.Config{},
		&models.MonitorHistory{},
		&models.MonitorState{},
		&models.User{},
	)
	if err != nil {
		return err
	}

	// 如果默认配置不存在，初始化默认配置
	initDefaultConfigs()

	// 初始化监控状态
	initMonitorStates(DB)

	// 创建默认用户
	if err := models.CreateDefaultUser(DB); err != nil {
		log.Printf("创建默认用户失败: %v", err)
	}

	return nil
}

// initDefaultConfigs 初始化默认配置
func initDefaultConfigs() {
	configs := []models.Config{
		// 监控间隔配置
		{
			Key:         string(models.ConfigCVEMonitorInterval),
			Value:       "60", // 60分钟
			Description: "CVE监控的间隔时间(分钟)",
		},
		{
			Key:         string(models.ConfigGitHubMonitorInterval),
			Value:       "120", // 120分钟
			Description: "GitHub监控的间隔时间(分钟)",
		},

		// 监控筛选条件
		{
			Key:         string(models.ConfigCVSSLevels),
			Value:       "high|critical", // 默认监控high和critical级别
			Description: "要监控的CVSS级别，可选值为low、medium、high、critical，多个级别用|分隔",
		},
		{
			Key:         string(models.ConfigCVEMaxAge),
			Value:       "30", // 默认30天
			Description: "CVE的最大年龄(天)，只记录在这个时间范围内创建的CVE，设置为0表示不限制",
		},

		// 功能开关配置
		{
			Key:         string(models.ConfigGitHubMonitorEnabled),
			Value:       "false",
			Description: "是否启用GitHub监控",
		},

		// API认证信息配置
		{
			Key:         string(models.ConfigOpenCVEUsername),
			Value:       "", // 需要用户配置
			Description: "OpenCVE 用户名",
		},
		{
			Key:         string(models.ConfigOpenCVEPassword),
			Value:       "", // 需要用户配置
			Description: "OpenCVE 密码",
		},
		{
			Key:         string(models.ConfigGitHubToken),
			Value:       "", // 需要用户配置
			Description: "GitHub API令牌",
		},

		// 通知配置
		{
			Key:         "notification_enabled",
			Value:       "true", // 默认启用
			Description: "是否启用通知功能",
		},
		{
			Key:         string(models.ConfigFeishuWebhookURL),
			Value:       "", // 需要用户配置
			Description: "飞书webhook URL",
		},

		// DeepSeek翻译配置
		{
			Key:         string(models.ConfigDeepSeekEnabled),
			Value:       "false", // 默认不启用
			Description: "是否启用DeepSeek翻译",
		},
		{
			Key:         string(models.ConfigDeepSeekAPIKey),
			Value:       "", // 需要用户配置
			Description: "DeepSeek API密钥",
		},

		// 最后运行时间已移动到MonitorState表中
	}

	for _, config := range configs {
		var existingConfig models.Config
		if err := DB.Where("key = ?", config.Key).First(&existingConfig).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				DB.Create(&config)
			}
		}
	}
}
