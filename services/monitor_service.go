package services

import (
	"cve-mo3/models"
	"cve-mo3/utils"
	"fmt"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"
)

// 全局互斥锁和状态变量
var (
	cveMonitorLock          sync.Mutex
	githubMonitorLock       sync.Mutex
	isCVEMonitorRunning     bool
	isGitHubMonitorRunning  bool
	isFirstCVEMonitorRun    = true // 系统启动标志，用于记录是否是系统启动后的第一次运行CVE监控
	isFirstGitHubMonitorRun = true // 系统启动标志，用于记录是否是系统启动后的第一次运行GitHub监控
)

// MonitorService 处理CVE和GitHub仓库的监控
type MonitorService struct {
	notificationService *NotificationService
}

// NewMonitorService 创建一个新的监控服务
func NewMonitorService() *MonitorService {
	return &MonitorService{
		notificationService: NewNotificationService(),
	}
}

// ShouldRunCVEMonitor 检查是否应该运行CVE监控
func (s *MonitorService) ShouldRunCVEMonitor() (bool, error) {
	// 如果已经有任务在运行，则不应该运行
	cveMonitorLock.Lock()
	defer cveMonitorLock.Unlock()
	if isCVEMonitorRunning {
		log.Printf("已有CVE监控任务正在运行，跳过本次执行")
		return false, nil
	}

	// 只在系统启动后的第一次运行时检查时间间隔
	if isFirstCVEMonitorRun {
		// 获取上次运行时间
		var lastRunTime time.Time
		var state models.MonitorState
		result := DB.Where("type = ?", models.MonitorStateCVE).First(&state)
		if result.Error == nil {
			lastRunTime = state.LastRunAt
		}

		// 获取监控间隔
		configService := NewConfigService()
		interval, err := configService.GetIntValue(models.ConfigCVEMonitorInterval)
		if err != nil {
			log.Printf("获取CVE监控间隔失败: %v", err)
			interval = 60 // 默认为60分钟
		}

		// 如果上次运行时间为零值，说明是第一次运行，应该运行
		if !lastRunTime.IsZero() {
			// 检查是否超过了监控间隔
			now := time.Now()
			duration := now.Sub(lastRunTime)
			if duration.Minutes() < float64(interval) {
				log.Printf("系统启动检查: 距离上次CVE监控运行时间 %.2f 分钟，小于设定的间隔 %d 分钟，跳过本次执行", duration.Minutes(), interval)
				isFirstCVEMonitorRun = false // 标记第一次运行已完成
				return false, nil
			}
		}

		// 标记第一次运行已完成
		isFirstCVEMonitorRun = false
		log.Printf("系统启动后第一次运行CVE监控，或者距离上次运行已超过设定间隔")
	}

	return true, nil
}

// RunCVEMonitor 运行CVE监控
func (s *MonitorService) RunCVEMonitor() error {
	// 检查是否应该运行
	shouldRun, err := s.ShouldRunCVEMonitor()
	if err != nil {
		return err
	}
	if !shouldRun {
		return nil
	}

	// 调用内部方法执行监控
	return s.runCVEMonitorInternal()
}

// RunCVEMonitorManually 手动运行CVE监控，绕过时间间隔检查
func (s *MonitorService) RunCVEMonitorManually() error {
	// 只检查是否有任务正在运行，不检查时间间隔
	cveMonitorLock.Lock()
	if isCVEMonitorRunning {
		cveMonitorLock.Unlock()
		log.Printf("已有CVE监控任务正在运行，跳过本次执行")
		return nil
	}
	cveMonitorLock.Unlock()

	// 调用内部方法执行监控
	return s.runCVEMonitorInternal()
}

// runCVEMonitorInternal 内部方法，实际执行监控逻辑
func (s *MonitorService) runCVEMonitorInternal() error {

	// 标记任务开始运行
	cveMonitorLock.Lock()
	isCVEMonitorRunning = true
	cveMonitorLock.Unlock()

	// 确保在函数结束时释放锁和更新运行时间
	defer func() {
		cveMonitorLock.Lock()
		isCVEMonitorRunning = false
		cveMonitorLock.Unlock()

		// 更新运行时间
		var state models.MonitorState
		result := DB.Where("type = ?", models.MonitorStateCVE).First(&state)
		if result.Error != nil {
			// 如果不存在，创建新的记录
			state = models.MonitorState{
				Type: models.MonitorStateCVE,
			}
		}
		state.LastRunAt = time.Now()
		DB.Save(&state)

		log.Printf("CVE监控任务完成")
	}()
	// 获取上次运行时间
	monitorStateService := NewMonitorStateService()
	lastRunTime, err := monitorStateService.GetLastRunTime(models.MonitorStateCVE)
	if err != nil {
		return err
	}

	// 获取OpenCVE用户名和密码
	var usernameConfig models.Config
	result := DB.Where("key = ?", string(models.ConfigOpenCVEUsername)).First(&usernameConfig)
	if result.Error != nil {
		return result.Error
	}

	var passwordConfig models.Config
	result = DB.Where("key = ?", string(models.ConfigOpenCVEPassword)).First(&passwordConfig)
	if result.Error != nil {
		return result.Error
	}

	// 创建 OpenCVE 客户端
	client := utils.NewOpenCVEClient(usernameConfig.Value, passwordConfig.Value)

	// 获取配置的CVSS级别
	var cvssLevelsConfig models.Config
	result = DB.Where("key = ?", string(models.ConfigCVSSLevels)).First(&cvssLevelsConfig)
	if result.Error != nil {
		log.Printf("获取CVSS级别配置失败: %v，使用默认级别 high|critical", result.Error)
		cvssLevelsConfig.Value = "high|critical"
	}

	// 获取CVE最大年龄配置
	var cveMaxAgeConfig models.Config
	result = DB.Where("key = ?", string(models.ConfigCVEMaxAge)).First(&cveMaxAgeConfig)
	if result.Error != nil {
		log.Printf("获取CVE最大年龄配置失败: %v，使用默认值 30天", result.Error)
		cveMaxAgeConfig.Value = "30"
	}

	// 解析CVE最大年龄
	cveMaxAge, err := strconv.Atoi(cveMaxAgeConfig.Value)
	if err != nil {
		log.Printf("解析CVE最大年龄失败: %v，使用默认值 30天", err)
		cveMaxAge = 30
	}

	// 分割CVSS级别
	cvssLevels := strings.Split(cvssLevelsConfig.Value, "|")
	log.Printf("配置的CVSS级别: %v", cvssLevels)

	// 所有获取到的CVE
	allCVEs := []utils.CVEItem{}

	// 创建CVE存在性检查函数
	existingCVEChecker := func(cveID string) bool {
		var count int64
		DB.Model(&models.CVE{}).Where("cve_id = ?", cveID).Count(&count)
		return count > 0
	}

	// 按照每个CVSS级别分别获取CVE
	for _, cvssLevel := range cvssLevels {
		cvssLevel = strings.TrimSpace(cvssLevel)
		if cvssLevel == "" {
			continue
		}

		// 使用优化的方法获取CVE：先检查数据库，再获取详情
		cves, err := client.GetCVEsByCVSSOptimized(lastRunTime, cvssLevel, cveMaxAge, existingCVEChecker)
		if err != nil {
			log.Printf("获取CVSS级别 %s 的CVE失败: %v", cvssLevel, err)
			continue
		}

		log.Printf("获取到 %d 条CVSS级别为 %s 的新CVE，包含详细信息", len(cves), cvssLevel)
		allCVEs = append(allCVEs, cves...)
	}

	// 如果没有找到新的CVE，这是正常情况，记录成功的运行
	if len(allCVEs) == 0 {
		log.Printf("没有找到新的CVE，这是正常情况")

		// 更新最后运行时间
		now := time.Now()
		monitorStateService := NewMonitorStateService()
		if err := monitorStateService.UpdateLastRunTime(models.MonitorStateCVE, now); err != nil {
			log.Printf("更新最后运行时间失败: %v", err)
		}

		s.recordMonitorRun(models.MonitorTypeCVE, models.MonitorStatusSuccess, "没有找到新的CVE", 0)
		return nil
	}

	// 使用合并后的CVE列表
	cves := allCVEs

	// 获取启用的规则
	var rules []models.Rule
	result = DB.Where("enabled = ?", true).Find(&rules)
	if result.Error != nil {
		return result.Error
	}

	// 处理CVE
	newCVEs := []string{}
	newCount := 0

	for _, cveItem := range cves {
		// 由于我们已经在前面过滤掉了已存在的CVE，这里的CVE都是新的
		// 但为了保险起见，我们仍然检查一下
		var existingCVE models.CVE
		result := DB.Where("cve_id = ?", cveItem.CVEID).First(&existingCVE)

		// 判断是新增还是更新
		isNewCVE := result.Error != nil

		// 如果CVE已存在，跳过（这种情况应该很少发生，因为我们已经预先过滤了）
		if !isNewCVE {
			log.Printf("CVE %s 已存在，跳过处理（这不应该发生）", cveItem.CVEID)
			continue
		}

		// 解析时间
		publishedAt, err := time.Parse(time.RFC3339, cveItem.CreatedAt)
		if err != nil {
			log.Printf("解析CVE创建时间失败: %v", err)
			continue
		}

		// 检查CVE是否匹配任何规则
		matchedRules := []string{}

		for _, rule := range rules {
			// 对每个规则，检查是否满足所有已设置的条件
			var keywordMatched, vendorMatched, productMatched, cvssMatched bool

			// 默认情况下，如果没有设置某个条件，则该条件视为已匹配
			keywordMatched = (rule.Keywords == "")
			vendorMatched = (rule.VendorName == "")
			productMatched = (rule.ProductName == "")

			// 检查CVSS评分
			var cvssScore float64
			if cveItem.Metrics != nil && cveItem.Metrics.CVSSV31 != nil && cveItem.Metrics.CVSSV31.Data != nil {
				cvssScore = cveItem.Metrics.CVSSV31.Data.Score
			}
			cvssMatched = (cvssScore >= rule.MinCVSSScore)

			// 检查关键字
			if rule.Keywords != "" {
				keywords := strings.Split(rule.Keywords, ",")
				for _, keyword := range keywords {
					keyword = strings.TrimSpace(keyword)
					if keyword != "" && (strings.Contains(strings.ToLower(cveItem.Description), strings.ToLower(keyword)) ||
						strings.Contains(strings.ToLower(cveItem.Title), strings.ToLower(keyword)) ||
						strings.Contains(strings.ToLower(cveItem.CVEID), strings.ToLower(keyword))) {
						keywordMatched = true
						log.Printf("CVE %s 匹配到关键字 '%s'", cveItem.CVEID, keyword)
						break
					}
				}
			}

			// 检查厂商
			if rule.VendorName != "" {
				for _, vendor := range cveItem.Vendors {
					if strings.Contains(strings.ToLower(vendor), strings.ToLower(rule.VendorName)) {
						vendorMatched = true
						log.Printf("CVE %s 匹配到厂商 '%s'", cveItem.CVEID, rule.VendorName)
						break
					}
				}
			}

			// 检查产品
			if rule.ProductName != "" {
				// 在新的API响应中，产品信息可能包含在厂商字符串中，格式为 "vendor$PRODUCT$product"
				for _, vendor := range cveItem.Vendors {
					if strings.Contains(vendor, "$PRODUCT$") && strings.Contains(strings.ToLower(vendor), strings.ToLower(rule.ProductName)) {
						productMatched = true
						log.Printf("CVE %s 匹配到产品 '%s'", cveItem.CVEID, rule.ProductName)
						break
					}
				}
			}

			// 如果所有已设置的条件都匹配，则添加规则
			if keywordMatched && vendorMatched && productMatched && cvssMatched {
				// 将规则ID转换为字符串
				ruleIDStr := fmt.Sprintf("%d", rule.ID)

				// 检查是否已经添加过该规则
				alreadyMatched := false
				for _, id := range matchedRules {
					if id == ruleIDStr {
						alreadyMatched = true
						break
					}
				}

				if !alreadyMatched {
					matchedRules = append(matchedRules, ruleIDStr)
					log.Printf("CVE %s 匹配规则 '%s'(ID: %s)，关键字匹配: %v，厂商匹配: %v，产品匹配: %v，CVSS评分匹配: %v (%.1f >= %.1f)",
						cveItem.CVEID, rule.Name, ruleIDStr, keywordMatched, vendorMatched, productMatched, cvssMatched, cvssScore, rule.MinCVSSScore)
				}
			}
		}

		// 如果没有匹配的规则，跳过该CVE
		if len(matchedRules) == 0 {
			continue
		}

		// 创建一个新的CVE记录
		// 解析更新时间（publishedAt已经在前面解析过了）
		modifiedAt, _ := time.Parse(time.RFC3339, cveItem.UpdatedAt)

		// 提取CVSS评分
		var cvssScore float64
		var cvssVector string
		if cveItem.Metrics != nil && cveItem.Metrics.CVSSV31 != nil && cveItem.Metrics.CVSSV31.Data != nil {
			cvssScore = cveItem.Metrics.CVSSV31.Data.Score
			cvssVector = cveItem.Metrics.CVSSV31.Data.Vector
		}

		// 提取厂商信息
		// 厂商通常只有一个，产品可能有多个
		var mainVendor string
		products := []string{}

		// 首先找到主要厂商（不包含$PRODUCT$的字符串）
		for _, vendor := range cveItem.Vendors {
			if !strings.Contains(vendor, "$PRODUCT$") {
				mainVendor = vendor
				break
			}
		}

		// 如果没有找到主要厂商，使用第一个包含$PRODUCT$的字符串的第一部分
		if mainVendor == "" && len(cveItem.Vendors) > 0 {
			for _, vendor := range cveItem.Vendors {
				if strings.Contains(vendor, "$PRODUCT$") {
					parts := strings.Split(vendor, "$PRODUCT$")
					if len(parts) >= 1 {
						mainVendor = parts[0]
						break
					}
				}
			}
		}

		// 提取所有产品
		for _, vendor := range cveItem.Vendors {
			if strings.Contains(vendor, "$PRODUCT$") {
				parts := strings.Split(vendor, "$PRODUCT$")
				if len(parts) >= 2 && parts[1] != "" {
					products = append(products, parts[1])
				}
			}
		}

		// 如果没有找到任何厂商信息，使用原始的vendors字符串
		if mainVendor == "" && len(cveItem.Vendors) > 0 {
			mainVendor = strings.Join(cveItem.Vendors, ", ")
		}

		// 提取弱点信息
		weaknesses := ""
		if len(cveItem.Weaknesses) > 0 {
			weaknesses = strings.Join(cveItem.Weaknesses, ", ")
		}

		cve := models.CVE{
			CVEID:        cveItem.CVEID,
			Title:        cveItem.Title,
			Description:  cveItem.Description,
			PublishedAt:  publishedAt,
			ModifiedAt:   modifiedAt,
			CVSSScore:    cvssScore,
			CVSSVector:   cvssVector,
			Weaknesses:   weaknesses,
			Vendor:       mainVendor,
			Product:      strings.Join(products, ", "),
			Status:       models.CVEStatusWatching,
			MatchedRules: strings.Join(matchedRules, ","),
			Translated:   false, // 默认未翻译
		}

		// 尝试翻译CVE
		translationService := NewTranslationService()
		if err := translationService.TranslateCVE(&cve); err != nil {
			log.Printf("翻译CVE时出错: %v", err)
			// 翻译失败不影响后续处理
		}

		// 保存新CVE（由于我们已经预先过滤，这里都是新CVE）
		if err := DB.Create(&cve).Error; err != nil {
			log.Printf("创建CVE时出错: %v", err)
			continue
		}

		// 添加到新CVE列表
		cvssInfo := ""
		if cve.CVSSScore > 0 {
			cvssInfo = fmt.Sprintf(" (CVSS: %.1f)", cve.CVSSScore)
		}
		newCVEs = append(newCVEs, fmt.Sprintf("- %s%s", cve.CVEID, cvssInfo))
		newCount++

		// 发送通知
		if err := s.notificationService.NotifyCVE(&cve); err != nil {
			log.Printf("发送通知时出错: %v", err)
		}
	}

	// 更新最后运行时间
	now := time.Now()
	if err := monitorStateService.UpdateLastRunTime(models.MonitorStateCVE, now); err != nil {
		return err
	}

	// 记录成功的运行
	s.recordMonitorRun(models.MonitorTypeCVE, models.MonitorStatusSuccess, "", newCount)

	// 发送摘要通知
	if len(newCVEs) > 0 {
		if err := s.notificationService.NotifyMonitorSummary(models.MonitorTypeCVE, newCount, newCVEs); err != nil {
			log.Printf("发送摘要通知时出错: %v", err)
		}
	}

	return nil
}

// ShouldRunGitHubMonitor 检查是否应该运行GitHub监控
func (s *MonitorService) ShouldRunGitHubMonitor() (bool, error) {
	// 检查是否启用GitHub监控
	configService := NewConfigService()
	enabled, err := configService.GetBoolValue(models.ConfigGitHubMonitorEnabled)
	if err != nil {
		log.Printf("获取GitHub监控启用状态失败: %v", err)
		// 如果无法获取配置，默认为禁用
		return false, nil
	}

	if !enabled {
		log.Printf("GitHub监控未启用，跳过执行")
		return false, nil
	}

	// 如果已经有任务在运行，则不应该运行
	githubMonitorLock.Lock()
	defer githubMonitorLock.Unlock()
	if isGitHubMonitorRunning {
		log.Printf("已有GitHub监控任务正在运行，跳过本次执行")
		return false, nil
	}

	// 只在系统启动后的第一次运行时检查时间间隔
	if isFirstGitHubMonitorRun {
		// 获取上次运行时间
		var lastRunTime time.Time
		var state models.MonitorState
		result := DB.Where("type = ?", models.MonitorStateGitHub).First(&state)
		if result.Error == nil {
			lastRunTime = state.LastRunAt
		}

		// 获取监控间隔
		configService := NewConfigService()
		interval, err := configService.GetIntValue(models.ConfigGitHubMonitorInterval)
		if err != nil {
			log.Printf("获取GitHub监控间隔失败: %v", err)
			interval = 120 // 默认为120分钟
		}

		// 如果上次运行时间为零值，说明是第一次运行，应该运行
		if !lastRunTime.IsZero() {
			// 检查是否超过了监控间隔
			now := time.Now()
			duration := now.Sub(lastRunTime)
			if duration.Minutes() < float64(interval) {
				log.Printf("系统启动检查: 距离上次GitHub监控运行时间 %.2f 分钟，小于设定的间隔 %d 分钟，跳过本次执行", duration.Minutes(), interval)
				isFirstGitHubMonitorRun = false // 标记第一次运行已完成
				return false, nil
			}
		}

		// 标记第一次运行已完成
		isFirstGitHubMonitorRun = false
		log.Printf("系统启动后第一次运行GitHub监控，或者距离上次运行已超过设定间隔")
	}

	return true, nil
}

// RunGitHubMonitor 运行GitHub监控
func (s *MonitorService) RunGitHubMonitor() error {
	// 检查是否应该运行
	shouldRun, err := s.ShouldRunGitHubMonitor()
	if err != nil {
		return err
	}
	if !shouldRun {
		return nil
	}

	// 调用内部方法执行监控
	return s.runGitHubMonitorInternal()
}

// RunGitHubMonitorManually 手动运行GitHub监控，绕过时间间隔检查
func (s *MonitorService) RunGitHubMonitorManually() error {
	// 检查是否启用GitHub监控
	configService := NewConfigService()
	enabled, err := configService.GetBoolValue(models.ConfigGitHubMonitorEnabled)
	if err != nil {
		log.Printf("获取GitHub监控启用状态失败: %v", err)
		return err
	}

	if !enabled {
		log.Printf("GitHub监控未启用，跳过执行")
		return nil
	}

	// 只检查是否有任务正在运行，不检查时间间隔
	githubMonitorLock.Lock()
	if isGitHubMonitorRunning {
		githubMonitorLock.Unlock()
		log.Printf("已有GitHub监控任务正在运行，跳过本次执行")
		return nil
	}
	githubMonitorLock.Unlock()

	// 调用内部方法执行监控
	return s.runGitHubMonitorInternal()
}

// runGitHubMonitorInternal 内部方法，实际执行监控逻辑
func (s *MonitorService) runGitHubMonitorInternal() error {

	// 标记任务开始运行
	githubMonitorLock.Lock()
	isGitHubMonitorRunning = true
	githubMonitorLock.Unlock()

	// 确保在函数结束时释放锁和更新运行时间
	defer func() {
		githubMonitorLock.Lock()
		isGitHubMonitorRunning = false
		githubMonitorLock.Unlock()

		// 更新运行时间
		var state models.MonitorState
		result := DB.Where("type = ?", models.MonitorStateGitHub).First(&state)
		if result.Error != nil {
			// 如果不存在，创建新的记录
			state = models.MonitorState{
				Type: models.MonitorStateGitHub,
			}
		}
		state.LastRunAt = time.Now()
		DB.Save(&state)

		log.Printf("GitHub监控任务完成")
	}()
	// 获取GitHub令牌（已经在外部检查了是否启用GitHub监控）
	var tokenConfig models.Config
	result := DB.Where("key = ?", string(models.ConfigGitHubToken)).First(&tokenConfig)
	if result.Error != nil {
		log.Printf("获取GitHub令牌失败: %v", result.Error)
		// 记录失败的运行
		s.recordMonitorRun(models.MonitorTypeGitHub, models.MonitorStatusFailed, "获取GitHub令牌失败", 0)
		return result.Error
	}

	// 创建GitHub客户端
	client := utils.NewGitHubClient(tokenConfig.Value)

	// 获取状态为"关注中"的CVE
	var cves []models.CVE
	result = DB.Where("status = ?", models.CVEStatusWatching).Find(&cves)
	if result.Error != nil {
		log.Printf("获取关注中的CVE失败: %v", result.Error)
		// 记录失败的运行
		s.recordMonitorRun(models.MonitorTypeGitHub, models.MonitorStatusFailed, "获取关注中的CVE失败", 0)
		return result.Error
	}

	// 如果没有关注中的CVE，直接返回
	if len(cves) == 0 {
		log.Printf("没有关注中的CVE，跳过GitHub监控")
		// 记录成功的运行
		s.recordMonitorRun(models.MonitorTypeGitHub, models.MonitorStatusSuccess, "没有关注中的CVE", 0)
		return nil
	}

	// 处理CVE
	newRepos := []string{}
	newCount := 0
	errorCount := 0 // 记录错误数量

	for _, cve := range cves {
		// 搜索与CVE相关的仓库
		// 确保使用正确的CVE ID格式
		cveID := cve.CVEID
		if !strings.HasPrefix(strings.ToUpper(cveID), "CVE-") {
			cveID = "CVE-" + cveID
		}
		repos, err := client.SearchRepositories(cveID)
		if err != nil {
			log.Printf("搜索仓库时出错: %v", err)
			// 记录错误，但继续处理其他CVE
			errorCount++
			continue
		}

		// 处理仓库
		for _, repoResult := range repos {
			// 检查仓库是否已存在
			var count int64
			result := DB.Model(&models.GitHubRepo{}).Where("repo_url = ?", repoResult.RepoURL).Count(&count)
			if result.Error != nil {
				log.Printf("检查仓库是否存在时出错: %v", result.Error)
				continue
			}

			// 如果仓库已存在，跳过
			if count > 0 {
				continue
			}

			// 创建一个新的GitHub仓库记录
			repo := models.GitHubRepo{
				CVEID:       cve.CVEID,
				RepoURL:     repoResult.RepoURL,
				RepoName:    repoResult.RepoName,
				Description: repoResult.Description,
				StarsCount:  repoResult.StarsCount,
			}

			// 保存仓库
			if err := DB.Create(&repo).Error; err != nil {
				log.Printf("创建仓库时出错: %v", err)
				continue
			}

			// 添加到新仓库列表
			newRepos = append(newRepos, fmt.Sprintf("- %s (%s)", repo.RepoName, repo.CVEID))
			newCount++

			// 发送通知
			if err := s.notificationService.NotifyGitHubRepo(&repo, &cve); err != nil {
				log.Printf("发送通知时出错: %v", err)
			}
		}
	}

	// 更新最后运行时间
	now := time.Now()
	monitorStateService := NewMonitorStateService()
	if err := monitorStateService.UpdateLastRunTime(models.MonitorStateGitHub, now); err != nil {
		log.Printf("更新最后运行时间失败: %v", err)
		// 记录失败的运行
		s.recordMonitorRun(models.MonitorTypeGitHub, models.MonitorStatusFailed, "更新最后运行时间失败", 0)
		return err
	}

	// 记录运行结果
	if errorCount > 0 {
		// 如果有错误，则将整个监控任务标记为失败
		s.recordMonitorRun(models.MonitorTypeGitHub, models.MonitorStatusFailed, fmt.Sprintf("在搜索仓库时发生了 %d 个错误", errorCount), newCount)
	} else if newCount == 0 {
		// 没有找到新的仓库，这是正常情况
		s.recordMonitorRun(models.MonitorTypeGitHub, models.MonitorStatusSuccess, "没有找到新的GitHub仓库", 0)
	} else {
		// 找到了新的仓库
		s.recordMonitorRun(models.MonitorTypeGitHub, models.MonitorStatusSuccess, "", newCount)
	}

	// 发送摘要通知
	if len(newRepos) > 0 {
		if err := s.notificationService.NotifyMonitorSummary(models.MonitorTypeGitHub, newCount, newRepos); err != nil {
			log.Printf("发送摘要通知时出错: %v", err)
		}
	}

	return nil
}

// recordMonitorRun 记录监控运行
func (s *MonitorService) recordMonitorRun(monitorType models.MonitorType, status models.MonitorStatus, message string, newCount int) {
	history := models.MonitorHistory{
		Type:     monitorType,
		RunTime:  time.Now(),
		Status:   status,
		Message:  message,
		NewCount: newCount,
	}

	if err := DB.Create(&history).Error; err != nil {
		log.Printf("记录监控运行时出错: %v", err)
	}
}

// parseBool 将字符串解析为布尔值
func parseBool(s string) (bool, error) {
	switch strings.ToLower(s) {
	case "true", "yes", "1", "on", "enabled":
		return true, nil
	case "false", "no", "0", "off", "disabled":
		return false, nil
	default:
		return false, fmt.Errorf("invalid boolean value: %s", s)
	}
}
