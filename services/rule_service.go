package services

import (
	"cve-mo3/models"
)

// RuleService 处理规则的业务逻辑
type RuleService struct {}

// NewRuleService 创建一个新的规则服务
func NewRuleService() *RuleService {
	return &RuleService{}
}

// GetAll 返回所有规则
func (s *RuleService) GetAll() ([]models.Rule, error) {
	var rules []models.Rule
	result := DB.Find(&rules)
	return rules, result.Error
}

// GetPaged 分页获取规则，支持搜索
func (s *RuleService) GetPaged(page, pageSize int, search string) ([]models.Rule, int64, error) {
	var rules []models.Rule
	var total int64

	// 构建查询
	db := DB.Model(&models.Rule{})

	// 添加搜索条件
	if search != "" {
		searchTerm := "%" + search + "%"
		db = db.Where("name LIKE ? OR keywords LIKE ? OR vendor_name LIKE ? OR product_name LIKE ?",
			searchTerm, searchTerm, searchTerm, searchTerm)
	}

	// 计算总数
	db.Count(&total)

	// 执行分页查询
	offset := (page - 1) * pageSize
	result := db.Offset(offset).Limit(pageSize).Order("id DESC").Find(&rules)

	return rules, total, result.Error
}

// GetEnabled 返回所有启用的规则
func (s *RuleService) GetEnabled() ([]models.Rule, error) {
	var rules []models.Rule
	result := DB.Where("enabled = ?", true).Find(&rules)
	return rules, result.Error
}

// GetByID 根据ID返回规则
func (s *RuleService) GetByID(id uint) (models.Rule, error) {
	var rule models.Rule
	result := DB.First(&rule, id)
	return rule, result.Error
}

// GetByName 根据名称返回规则
func (s *RuleService) GetByName(name string) (models.Rule, error) {
	var rule models.Rule
	result := DB.Where("name = ?", name).First(&rule)
	return rule, result.Error
}

// ExistsByName 检查指定名称的规则是否存在
func (s *RuleService) ExistsByName(name string) (bool, error) {
	var count int64
	result := DB.Model(&models.Rule{}).Where("name = ?", name).Count(&count)
	return count > 0, result.Error
}

// Create 创建一个新的规则
func (s *RuleService) Create(rule *models.Rule) error {
	return DB.Create(rule).Error
}

// Update 更新规则
func (s *RuleService) Update(rule *models.Rule) error {
	return DB.Save(rule).Error
}

// Delete 删除规则
func (s *RuleService) Delete(id uint) error {
	return DB.Delete(&models.Rule{}, id).Error
}
