package services

import (
	"cve-mo3/models"
	"errors"
)

// UserService 用户服务
type UserService struct{}

// NewUserService 创建用户服务实例
func NewUserService() *UserService {
	return &UserService{}
}

// ValidateUser 验证用户登录
func (s *UserService) ValidateUser(username, password string) (*models.User, error) {
	var user models.User
	
	// 根据用户名查找用户
	if err := DB.Where("username = ?", username).First(&user).Error; err != nil {
		return nil, errors.New("用户不存在")
	}

	// 验证密码
	if !user.CheckPassword(password) {
		return nil, errors.New("密码错误")
	}

	return &user, nil
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(id uint) (*models.User, error) {
	var user models.User
	if err := DB.First(&user, id).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

// ChangePassword 修改密码
func (s *UserService) ChangePassword(userID uint, oldPassword, newPassword string) error {
	// 获取用户
	var user models.User
	if err := DB.First(&user, userID).Error; err != nil {
		return errors.New("用户不存在")
	}

	// 验证旧密码
	if !user.CheckPassword(oldPassword) {
		return errors.New("原密码错误")
	}

	// 设置新密码
	if err := user.SetPassword(newPassword); err != nil {
		return errors.New("密码加密失败")
	}

	// 保存到数据库
	if err := DB.Save(&user).Error; err != nil {
		return errors.New("密码保存失败")
	}

	return nil
}

// CreateUser 创建用户
func (s *UserService) CreateUser(username, password string) (*models.User, error) {
	// 检查用户名是否已存在
	var existingUser models.User
	if err := DB.Where("username = ?", username).First(&existingUser).Error; err == nil {
		return nil, errors.New("用户名已存在")
	}

	// 创建新用户
	user := &models.User{
		Username: username,
	}
	
	if err := user.SetPassword(password); err != nil {
		return nil, errors.New("密码加密失败")
	}

	if err := DB.Create(user).Error; err != nil {
		return nil, errors.New("用户创建失败")
	}

	return user, nil
}

// UpdateUser 更新用户信息
func (s *UserService) UpdateUser(userID uint, username string) error {
	var user models.User
	if err := DB.First(&user, userID).Error; err != nil {
		return errors.New("用户不存在")
	}

	// 检查新用户名是否已被其他用户使用
	if user.Username != username {
		var existingUser models.User
		if err := DB.Where("username = ? AND id != ?", username, userID).First(&existingUser).Error; err == nil {
			return errors.New("用户名已存在")
		}
	}

	user.Username = username
	if err := DB.Save(&user).Error; err != nil {
		return errors.New("用户信息更新失败")
	}

	return nil
}
