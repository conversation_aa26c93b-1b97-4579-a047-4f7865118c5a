<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CVE 监控系统</title>
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Vue 3 -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <!-- Vue Router -->
  <script src="https://unpkg.com/vue-router@4"></script>
  <!-- Axios -->
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <!-- Custom styles -->
  <style>
    body {
      font-family: 'Inter', sans-serif;
      background-color: #f9fafb;
    }

    .gradient-bg {
      background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    }

    .card {
      background-color: white;
      border-radius: 0.75rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
    }

    .card:hover {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      /* 移除浮动效果 */
      /* transform: translateY(-2px); */
    }

    .btn-primary {
      background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
      color: white;
      border-radius: 0.5rem;
      padding: 0.5rem 1rem;
      transition: all 0.3s ease;
    }

    .btn-primary:hover {
      opacity: 0.9;
      transform: translateY(-1px);
    }
  </style>
</head>
<body>
  <div id="app"></div>

  <script type="module">
    // 将在这里导入和挂载Vue应用
    import App from '/js/App.js';

    // 使用全局变量而不是导入
    const { createRouter, createWebHistory } = VueRouter;
    const { createApp } = Vue;

    // 导入视图组件
    import Dashboard from '/js/views/Dashboard.js';
    import CVEView from '/js/views/CVEView.js';
    import RulesView from '/js/views/RulesView.js';
    import GithubView from '/js/views/GithubView.js';
    import SettingsView from '/js/views/SettingsView.js';
    import LoginView from '/js/views/LoginView.js';

    // 创建路由
    const routes = [
      { path: '/login', component: LoginView, name: '登录' },
      { path: '/', component: Dashboard, name: '仪表盘' },
      { path: '/cves', component: CVEView, name: 'CVE 管理' },
      { path: '/cves/:id', component: CVEView, name: 'CVE 详情', props: true },
      { path: '/rules', component: RulesView, name: '规则管理' },
      { path: '/github', component: GithubView, name: 'GitHub 仓库' },
      { path: '/settings', component: SettingsView, name: '系统设置' }
    ];

    const router = createRouter({
      history: createWebHistory(),
      routes
    });

    // 创建并挂载应用
    const app = createApp(App);
    app.use(router);
    app.mount('#app');
  </script>
</body>
</html>
