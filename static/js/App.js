import Sidebar from './components/common/Sidebar.js';
import Navbar from './components/common/Navbar.js';
import authService from './services/authService.js';

export default {
  components: {
    Sidebar,
    Navbar
  },
  setup() {
    const { ref, onMounted } = Vue;

    const isAuthenticated = ref(false);

    // 检查认证状态
    const checkAuth = () => {
      isAuthenticated.value = authService.isAuthenticated();
      if (!isAuthenticated.value) {
        // 如果未认证，跳转到登录页
        window.location.href = '/login';
      }
    };

    onMounted(() => {
      checkAuth();
    });

    return {
      isAuthenticated
    };
  },
  template: `
    <div v-if="isAuthenticated" class="flex h-screen bg-gray-50">
      <!-- Sidebar -->
      <Sidebar />

      <!-- Main Content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top Navbar -->
        <Navbar />

        <!-- Main Content Area -->
        <main class="flex-1 overflow-y-auto p-6">
          <router-view></router-view>
        </main>
      </div>
    </div>
    <div v-else class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p class="mt-4 text-gray-600">正在验证身份...</p>
      </div>
    </div>
  `
};
