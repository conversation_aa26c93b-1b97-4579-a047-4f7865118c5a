import authService from '../services/authService.js';

export default {
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'success'],
  setup(props, { emit }) {
    const { ref, reactive, watch } = Vue;
    
    // 响应式数据
    const loading = ref(false);
    const form = reactive({
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    const error = ref('');
    const success = ref('');

    // 重置表单
    const resetForm = () => {
      form.oldPassword = '';
      form.newPassword = '';
      form.confirmPassword = '';
      error.value = '';
      success.value = '';
    };

    // 监听显示状态变化
    watch(() => props.show, (newVal) => {
      if (newVal) {
        resetForm();
      }
    });

    // 验证表单
    const validateForm = () => {
      if (!form.oldPassword) {
        error.value = '请输入原密码';
        return false;
      }
      if (!form.newPassword) {
        error.value = '请输入新密码';
        return false;
      }
      if (form.newPassword.length < 6) {
        error.value = '新密码长度至少6位';
        return false;
      }
      if (form.newPassword !== form.confirmPassword) {
        error.value = '两次输入的新密码不一致';
        return false;
      }
      return true;
    };

    // 修改密码
    const changePassword = async () => {
      if (!validateForm()) {
        return;
      }

      loading.value = true;
      error.value = '';
      success.value = '';

      try {
        await authService.changePassword(form.oldPassword, form.newPassword);
        success.value = '密码修改成功';
        
        // 2秒后关闭弹窗
        setTimeout(() => {
          emit('success');
          emit('close');
        }, 2000);
      } catch (err) {
        console.error('修改密码失败:', err);
        if (err.response && err.response.data && err.response.data.message) {
          error.value = err.response.data.message;
        } else {
          error.value = '修改密码失败，请重试';
        }
      } finally {
        loading.value = false;
      }
    };

    // 关闭弹窗
    const close = () => {
      emit('close');
    };

    return {
      form,
      loading,
      error,
      success,
      changePassword,
      close
    };
  },
  template: `
    <div v-if="show" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="close">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white" @click.stop>
        <div class="mt-3">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">修改密码</h3>
            <button @click="close" class="text-gray-400 hover:text-gray-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <form @submit.prevent="changePassword" class="space-y-4">
            <div>
              <label for="oldPassword" class="block text-sm font-medium text-gray-700">原密码</label>
              <input
                id="oldPassword"
                type="password"
                v-model="form.oldPassword"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="请输入原密码"
              />
            </div>
            
            <div>
              <label for="newPassword" class="block text-sm font-medium text-gray-700">新密码</label>
              <input
                id="newPassword"
                type="password"
                v-model="form.newPassword"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="请输入新密码（至少6位）"
              />
            </div>
            
            <div>
              <label for="confirmPassword" class="block text-sm font-medium text-gray-700">确认新密码</label>
              <input
                id="confirmPassword"
                type="password"
                v-model="form.confirmPassword"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="请再次输入新密码"
              />
            </div>
            
            <div v-if="error" class="text-red-600 text-sm">
              {{ error }}
            </div>
            
            <div v-if="success" class="text-green-600 text-sm">
              {{ success }}
            </div>
            
            <div class="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                @click="close"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                取消
              </button>
              <button
                type="submit"
                :disabled="loading"
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {{ loading ? '修改中...' : '确认修改' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `
};
