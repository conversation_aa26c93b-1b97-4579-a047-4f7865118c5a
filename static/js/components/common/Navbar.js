import authService from '../../services/authService.js';
import ChangePasswordModal from '../ChangePasswordModal.js';

export default {
  components: {
    ChangePasswordModal
  },
  setup() {
    const { ref } = Vue;

    const currentDate = new Date().toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const showUserMenu = ref(false);
    const showChangePasswordModal = ref(false);
    const user = ref(authService.getUser());

    // 退出登录
    const logout = async () => {
      try {
        await authService.logout();
        window.location.href = '/login';
      } catch (error) {
        console.error('退出登录失败:', error);
        // 即使退出请求失败，也清除本地数据并跳转
        authService.clearAuth();
        window.location.href = '/login';
      }
    };

    // 显示修改密码弹窗
    const showChangePassword = () => {
      showUserMenu.value = false;
      showChangePasswordModal.value = true;
    };

    // 关闭修改密码弹窗
    const closeChangePassword = () => {
      showChangePasswordModal.value = false;
    };

    // 密码修改成功
    const onPasswordChanged = () => {
      alert('密码修改成功，请重新登录');
      logout();
    };

    return {
      currentDate,
      showUserMenu,
      showChangePasswordModal,
      user,
      logout,
      showChangePassword,
      closeChangePassword,
      onPasswordChanged
    };
  },
  template: `
    <header class="bg-white border-b border-gray-200">
      <div class="flex items-center justify-between px-6 py-4">
        <!-- Left: Page Title & Breadcrumb -->
        <div>
          <h2 class="text-xl font-semibold text-gray-800">{{ $route.name || '仪表盘' }}</h2>
          <p class="text-sm text-gray-500">{{ currentDate }}</p>
        </div>
        
        <!-- Right: Actions -->
        <div class="flex items-center space-x-4">
          <!-- Search -->
          <div class="relative">
            <input 
              type="text" 
              placeholder="搜索..." 
              class="w-64 pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          
          <!-- Notification -->
          <button class="p-2 rounded-full hover:bg-gray-100 transition-colors">
            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
            </svg>
          </button>
          
          <!-- Refresh -->
          <button class="p-2 rounded-full hover:bg-gray-100 transition-colors">
            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
          </button>

          <!-- User Menu -->
          <div class="relative">
            <button
              @click="showUserMenu = !showUserMenu"
              class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                <span class="text-white text-sm font-medium">{{ user?.username?.charAt(0)?.toUpperCase() || 'U' }}</span>
              </div>
              <span class="text-sm font-medium text-gray-700">{{ user?.username || '用户' }}</span>
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>

            <!-- Dropdown Menu -->
            <div v-if="showUserMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
              <button
                @click="showChangePassword"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                </svg>
                修改密码
              </button>
              <hr class="my-1">
              <button
                @click="logout"
                class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
              >
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                </svg>
                退出登录
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Change Password Modal -->
    <ChangePasswordModal
      :show="showChangePasswordModal"
      @close="closeChangePassword"
      @success="onPasswordChanged"
    />
  `
};
