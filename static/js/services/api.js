// 使用全局变量 axios
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    return config;
  },
  error => {
    // 对请求错误做些什么
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    const res = response.data;

    // 如果返回的状态码不是200，说明接口请求有误
    if (res.code !== 200 && res.code !== undefined) {
      // 显示错误消息
      console.error(res.message || '请求失败');
      return Promise.reject(new Error(res.message || '请求失败'));
    }

    return res;
  },
  error => {
    // 对响应错误做点什么
    console.error('请求失败: ' + error.message);

    // 如果是401错误，清除认证信息并跳转到登录页
    if (error.response && error.response.status === 401) {
      // 动态导入authService以避免循环依赖
      import('./authService.js').then(module => {
        const authService = module.default;
        authService.clearAuth();

        // 如果当前不在登录页，则跳转到登录页
        if (window.location.pathname !== '/login') {
          window.location.href = '/login';
        }
      });
    }

    return Promise.reject(error);
  }
);

export default api;
