import api from './api.js';

class AuthService {
  constructor() {
    this.tokenKey = 'auth_token';
    this.userKey = 'auth_user';
  }

  // 登录
  async login(username, password) {
    try {
      const response = await api.post('/auth/login', {
        username,
        password
      });

      if (response.data && response.data.token) {
        // 保存token和用户信息
        this.setToken(response.data.token);
        this.setUser(response.data.user);
        
        // 设置API默认认证头
        this.setAuthHeader(response.data.token);
        
        return response;
      }
      
      throw new Error('登录响应格式错误');
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  }

  // 退出登录
  async logout() {
    try {
      // 调用后端退出接口
      await api.post('/auth/logout');
    } catch (error) {
      console.error('退出登录请求失败:', error);
    } finally {
      // 无论后端请求是否成功，都清除本地数据
      this.clearAuth();
    }
  }

  // 修改密码
  async changePassword(oldPassword, newPassword) {
    return await api.post('/user/change-password', {
      old_password: oldPassword,
      new_password: newPassword
    });
  }

  // 获取用户信息
  async getProfile() {
    return await api.get('/user/profile');
  }

  // 刷新token
  async refreshToken() {
    try {
      const response = await api.post('/auth/refresh');
      if (response.data && response.data.token) {
        this.setToken(response.data.token);
        this.setAuthHeader(response.data.token);
        return response.data.token;
      }
      throw new Error('刷新token失败');
    } catch (error) {
      console.error('刷新token失败:', error);
      this.clearAuth();
      throw error;
    }
  }

  // 保存token
  setToken(token) {
    localStorage.setItem(this.tokenKey, token);
  }

  // 获取token
  getToken() {
    return localStorage.getItem(this.tokenKey);
  }

  // 保存用户信息
  setUser(user) {
    localStorage.setItem(this.userKey, JSON.stringify(user));
  }

  // 获取用户信息
  getUser() {
    const userStr = localStorage.getItem(this.userKey);
    return userStr ? JSON.parse(userStr) : null;
  }

  // 检查是否已登录
  isAuthenticated() {
    const token = this.getToken();
    if (!token) return false;

    // 检查token是否过期（简单检查）
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const now = Date.now() / 1000;
      return payload.exp > now;
    } catch (error) {
      console.error('Token解析失败:', error);
      return false;
    }
  }

  // 设置API认证头
  setAuthHeader(token) {
    if (token) {
      api.defaults.headers.common['X-Auth-Token'] = token;
    } else {
      delete api.defaults.headers.common['X-Auth-Token'];
    }
  }

  // 清除认证信息
  clearAuth() {
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.userKey);
    delete api.defaults.headers.common['X-Auth-Token'];
  }

  // 初始化认证状态
  initAuth() {
    const token = this.getToken();
    if (token && this.isAuthenticated()) {
      this.setAuthHeader(token);
      return true;
    } else {
      this.clearAuth();
      return false;
    }
  }
}

// 创建单例实例
const authService = new AuthService();

// 初始化认证状态
authService.initAuth();

export default authService;
