import api from './api.js';

export default {
  // 获取所有规则
  getAllRules() {
    return api.get('/rules');
  },

  // 分页获取规则
  getRulesPaged(params) {
    return api.get('/rules', { params });
  },
  
  // 获取单个规则详情
  getRuleById(id) {
    return api.get(`/rules/${id}`);
  },
  
  // 创建规则
  createRule(rule) {
    return api.post('/rules', rule);
  },
  
  // 更新规则
  updateRule(id, rule) {
    return api.put(`/rules/${id}`, rule);
  },
  
  // 删除规则
  deleteRule(id) {
    return api.delete(`/rules/${id}`);
  }
};
