import Card from '../components/common/Card.js';
import cveService from '../services/cveService.js';
import githubService from '../services/githubService.js';
import ruleService from '../services/ruleService.js';
import translationService from '../services/translationService.js';

export default {
  components: {
    Card
  },
  props: {
    id: {
      type: String,
      default: null
    },
    from: {
      type: String,
      default: 'list'
    }
  },
  setup(props) {
    const { ref, reactive, computed, onMounted, watch } = Vue;
    const router = VueRouter.useRouter();
    const route = VueRouter.useRoute();

    // 获取来源页面参数
    const from = ref(route.query.from || 'list');

    // 状态
    const cves = ref([]);
    const loading = ref(true);
    const selectedCVE = ref(null);
    const githubRepos = ref([]);
    const loadingRepos = ref(false);
    const ruleNames = ref({});
    const translating = ref(false); // 添加翻译状态变量

    // 记录上次检查到的翻译完成事件时间戳
    const lastCompletedTimestamp = ref(0);

    // 过滤和分页
    const filters = reactive({
      search: '',
      status: 'watching', // 默认为关注中
      min_cvss: 0,
      vendor: '',
      product: ''
    });

    const pagination = reactive({
      page: 1,
      page_size: 10,
      total: 0
    });

    // 计算属性
    const totalPages = computed(() => {
      return Math.ceil(pagination.total / pagination.page_size);
    });

    // 方法
    const loadCVEs = async () => {
      loading.value = true;
      try {
        // 构建查询参数
        const params = {
          page: pagination.page,
          page_size: pagination.page_size,
          ...filters
        };

        const response = await cveService.getCVEs(params);
        if (response.data) {
          cves.value = response.data.list;
          pagination.total = response.data.pagination.total;
        }
      } catch (error) {
        console.error('加载CVE数据失败:', error);
      } finally {
        loading.value = false;
      }
    };



    // 查询按钮点击事件
    const handleSearch = () => {
      pagination.page = 1; // 重置到第一页
      loadCVEs();
    };

    // 翻译CVE
    const translateCVE = async (id) => {
      // 检查当前翻译状态
      const status = translationService.getTranslationStatus();
      if (status.translating) {
        // 如果已经有翻译正在进行，则不允许再次翻译
        alert('有翻译正在进行中，请稍后再试');
        return;
      }

      // 设置本地翻译状态
      translating.value = true;

      try {
        const response = await translationService.translateCVE(id);
        if (response.code === 200) {
          // 重新加载CVE详情
          loadCVEDetail(id);
        } else {
          // 显示错误信息
          if (response.message.includes('翻译功能未开启')) {
            // 如果是翻译功能未开启，提示用户前往设置页面
            if (confirm(`${response.message}\n\n是否前往系统设置页面开启翻译功能？`)) {
              // 跳转到设置页面
              window.location.href = '/settings';
            }
          } else if (response.message.includes('API密钥未配置')) {
            // 如果是API密钥未配置，提示用户前往设置页面
            if (confirm(`${response.message}\n\n是否前往系统设置页面配置API密钥？`)) {
              // 跳转到设置页面
              window.location.href = '/settings';
            }
          } else {
            // 其他错误
            alert(`翻译失败: ${response.message}`);
          }
        }
      } catch (error) {
        console.error('翻译CVE失败:', error);
        alert('翻译失败，请稍后重试');
      } finally {
        // 无论成功还是失败，都重置本地翻译状态
        // 注意：translationService.translateCVE 已经处理了共享状态的重置
        translating.value = false;

        // 再次检查状态，确保状态同步
        checkTranslationStatus();
      }
    };

    const selectCVE = async (cve) => {
      // 调用CVE详情API获取完整信息（包括规则名称和GitHub仓库）
      console.log('选择CVE，开始加载详情:', cve.id);

      // 先设置基本信息，避免界面闪烁
      selectedCVE.value = cve;

      // 调用详情API获取完整信息
      try {
        const response = await cveService.getCVEById(cve.id);
        console.log('获取到CVE详情响应:', response);

        if (response.data && response.data.cve) {
          // 更新选中的CVE为完整信息
          selectedCVE.value = response.data.cve;
          console.log('更新选中的CVE为完整信息:', selectedCVE.value);

          // 如果后端直接返回了规则名称映射
          if (response.data.rule_names) {
            ruleNames.value = response.data.rule_names;
            console.log('使用后端返回的规则名称:', ruleNames.value);
          } else {
            // 否则加载规则名称
            if (selectedCVE.value.matched_rules) {
              loadRuleNames(selectedCVE.value.matched_rules);
            } else {
              console.log('没有匹配的规则');
              ruleNames.value = {};
            }
          }

          // 如果后端直接返回了GitHub仓库信息
          if (response.data.github_repos) {
            githubRepos.value = response.data.github_repos;
            console.log('使用后端返回的GitHub仓库:', githubRepos.value);
            loadingRepos.value = false;
          } else {
            // 否则加载GitHub仓库（保留兼容性）
            loadingRepos.value = true;
            try {
              // 确保cve_id存在
              if (selectedCVE.value.cve_id) {
                console.log('开始获取GitHub仓库，CVE ID:', selectedCVE.value.cve_id);
                const repoResponse = await githubService.getReposByCVE(selectedCVE.value.cve_id);
                if (repoResponse.data) {
                  githubRepos.value = repoResponse.data;
                }
              } else {
                console.warn('CVE ID不存在，无法获取GitHub仓库');
                githubRepos.value = [];
              }
            } catch (error) {
              console.error('加载GitHub仓库失败:', error);
              githubRepos.value = [];
            } finally {
              loadingRepos.value = false;
            }
          }
        }
      } catch (error) {
        console.error('加载CVE详情失败:', error);
        // 如果详情API失败，回退到原来的逻辑
        if (cve.matched_rules) {
          loadRuleNames(cve.matched_rules);
        } else {
          console.log('没有匹配的规则');
          ruleNames.value = {};
        }

        // 加载相关的GitHub仓库
        loadingRepos.value = true;
        try {
          if (cve.cve_id) {
            console.log('开始获取GitHub仓库，CVE ID:', cve.cve_id);
            const response = await githubService.getReposByCVE(cve.cve_id);
            if (response.data) {
              githubRepos.value = response.data;
            }
          } else {
            console.warn('CVE ID不存在，无法获取GitHub仓库');
            githubRepos.value = [];
          }
        } catch (error) {
          console.error('加载GitHub仓库失败:', error);
          githubRepos.value = [];
        } finally {
          loadingRepos.value = false;
        }
      }
    };

    const updateCVEStatus = async (id, status) => {
      try {
        await cveService.updateCVEStatus(id, status);

        // 更新本地数据
        const index = cves.value.findIndex(cve => cve.id === id);
        if (index !== -1) {
          cves.value[index].status = status;
        }

        if (selectedCVE.value && selectedCVE.value.id === id) {
          selectedCVE.value.status = status;
        }
      } catch (error) {
        console.error('更新CVE状态失败:', error);
      }
    };

    const resetFilters = () => {
      filters.search = '';
      filters.status = 'watching'; // 重置为关注中
      filters.min_cvss = 0;
      filters.vendor = '';
      filters.product = '';
      pagination.page = 1;
      loadCVEs(); // 重置后自动加载数据
    };

    const changePage = (page) => {
      pagination.page = page;
      loadCVEs(); // 切换页面后自动加载数据
    };

    // 监听路由变化
    watch(() => props.id, (newId, oldId) => {
      console.log('路由变化检测:', { newId, oldId });
      if (newId !== oldId) {
        if (newId) {
          // 切换到详情页
          loadCVEDetail(newId);
        } else {
          // 切换到列表页
          // 清除之前的选中状态
          selectedCVE.value = null;
          githubRepos.value = [];
          ruleNames.value = {};
          loadCVEs();
        }
      }
    });

    // 生命周期钩子
    onMounted(() => {
      if (props.id) {
        // 如果有ID参数，加载单个CVE详情
        loadCVEDetail(props.id);
      } else {
        // 否则加载所有CVE
        // 清除之前可能存在的选中状态
        selectedCVE.value = null;
        githubRepos.value = [];
        ruleNames.value = {};
        loadCVEs();
      }

      // 在加载数据后检查翻译状态
      // 这样可以确保 loadCVEDetail 已经定义
      setTimeout(() => {
        checkTranslationStatus(); // 初始检查，这会启动定期检查
      }, 100);
    });

    // 加载规则名称
    const loadRuleNames = async (ruleIDs) => {
      console.log('开始加载规则名称，规则ID列表:', ruleIDs);

      if (!ruleIDs) {
        console.log('规则ID列表为空，返回空映射');
        ruleNames.value = {};
        return;
      }

      try {
        // 获取规则列表
        console.log('开始获取规则列表...');
        const response = await ruleService.getAllRules();
        console.log('获取规则列表响应:', response);

        if (response.data) {
          console.log('获取到规则列表:', response.data);
          const rules = response.data;
          const ruleMap = {};

          try {
            // 解析规则ID列表
            const ids = ruleIDs.split(',');
            console.log('规则ID列表:', ids);

            // 为每个ID查找对应的规则名称
            ids.forEach(id => {
              console.log('当前处理的规则ID:', id);
              const rule = rules.find(r => r.id === parseInt(id));
              console.log('找到的规则:', rule);
              ruleMap[id] = rule ? rule.name : '未知规则';
              console.log('设置规则名称:', id, '->', ruleMap[id]);
            });
          } catch (parseError) {
            console.error('解析规则ID列表失败:', parseError);
            // 如果解析失败，返回空映射
          }

          console.log('规则名称映射对象:', ruleMap);
          ruleNames.value = ruleMap;
          console.log('设置规则名称映射完成:', ruleNames.value);
        } else {
          console.log('规则列表响应数据为空');
          ruleNames.value = {};
        }
      } catch (error) {
        console.error('加载规则名称失败:', error);
        ruleNames.value = {};
      }
    };

    // 加载CVE详情
    const loadCVEDetail = async (id) => {
      loading.value = true;
      selectedCVE.value = null; // 重置选中的CVE
      githubRepos.value = []; // 重置相关的GitHub仓库

      console.log('开始加载CVE详情，ID:', id);

      try {
        const response = await cveService.getCVEById(id);
        console.log('获取到CVE详情响应:', response);
        console.log('响应数据:', response.data);
        console.log('响应数据类型:', typeof response.data);

        if (response.data) {
          console.log('响应中的CVE数据:', response.data);

          // 处理返回的数据结构
          if (response.data.cve) {
            // 设置选中的CVE
            selectedCVE.value = response.data.cve;
            console.log('设置选中的CVE:', selectedCVE.value);
            console.log('翻译状态:', selectedCVE.value.translated, typeof selectedCVE.value.translated);

            // 如果后端直接返回了规则名称映射
            if (response.data.rule_names) {
              ruleNames.value = response.data.rule_names;
              console.log('使用后端返回的规则名称:', ruleNames.value);
            } else {
              // 否则加载规则名称
              loadRuleNames(selectedCVE.value.matched_rules);
            }

            // 如果后端直接返回了GitHub仓库信息
            if (response.data.github_repos) {
              githubRepos.value = response.data.github_repos;
              console.log('使用后端返回的GitHub仓库:', githubRepos.value);
              loadingRepos.value = false;
            } else {
              // 否则加载GitHub仓库（保留兼容性）
              loadingRepos.value = true;
              try {
                // 确保cve_id存在
                if (selectedCVE.value.cve_id) {
                  console.log('开始获取GitHub仓库，CVE ID:', selectedCVE.value.cve_id);
                  const repoResponse = await githubService.getReposByCVE(selectedCVE.value.cve_id);
                  console.log('获取到GitHub仓库响应:', repoResponse);

                  if (repoResponse.data) {
                    githubRepos.value = repoResponse.data;
                    console.log('设置相关的GitHub仓库:', githubRepos.value);
                  }
                } else {
                  console.warn('CVE ID不存在，无法获取GitHub仓库');
                  githubRepos.value = [];
                }
              } catch (error) {
                console.error('加载GitHub仓库失败:', error);
                githubRepos.value = [];
              } finally {
                loadingRepos.value = false;
              }
            }

            console.log('匹配的规则ID:', selectedCVE.value.matched_rules);
          } else {
            console.error('响应中没有CVE数据');
          }
        } else {
          console.error('响应数据不完整:', response);
        }
      } catch (error) {
        console.error('加载CVE详情失败:', error);
      } finally {
        loading.value = false;
      }
    };

    // 初始化时检查翻译状态
    const checkTranslationStatus = () => {
      // 检查翻译状态
      const status = translationService.getTranslationStatus();
      const prevTranslating = translating.value;
      translating.value = status.translating;

      // 如果翻译状态从正在翻译变为已完成，则检查完成事件
      if (prevTranslating && !translating.value) {
        console.log('检测到翻译状态变化，从正在翻译变为已完成');

        // 如果当前正在查看的是这个CVE，则刷新数据
        if (props.id && status.cveId && props.id === String(status.cveId)) {
          console.log('刷新当前查看的CVE数据:', props.id);
          loadCVEDetail(props.id);
        } else if (selectedCVE.value && status.cveId && selectedCVE.value.id === status.cveId) {
          // 如果在列表页面并且当前选中的CVE是翻译完成的CVE
          console.log('刷新当前选中的CVE数据:', selectedCVE.value.id);
          loadCVEDetail(selectedCVE.value.id);
        }
      }

      // 检查是否有翻译完成的事件
      const completed = translationService.getTranslationCompleted();
      if (completed && completed.cveId && completed.timestamp > lastCompletedTimestamp.value) {
        console.log('检测到新的翻译完成事件:', completed);
        lastCompletedTimestamp.value = completed.timestamp;

        // 如果当前正在查看的是这个CVE，则刷新数据
        if (props.id && props.id === String(completed.cveId)) {
          console.log('刷新当前查看的CVE数据:', props.id);
          loadCVEDetail(props.id);
        } else if (selectedCVE.value && selectedCVE.value.id === completed.cveId) {
          // 如果在列表页面并且当前选中的CVE是翻译完成的CVE
          console.log('刷新当前选中的CVE数据:', selectedCVE.value.id);
          loadCVEDetail(selectedCVE.value.id);
        }
      }

      // 定期检查状态，无论是否正在翻译
      // 这确保了即使在其他页面完成翻译，当前页面也能及时检测到
      setTimeout(checkTranslationStatus, 2000); // 每2秒检查一次
    };

    // 删除CVE
    const deleteCVE = async (id) => {
      if (!confirm('确定要删除这个CVE吗？删除后将无法恢复，相关的GitHub仓库也会被删除。')) {
        return;
      }

      try {
        await cveService.deleteCVE(id);
        // 删除成功后，重新加载数据
        if (props.id) {
          // 如果是详情页，返回列表页
          router.push('/cves');
        } else {
          // 如果是列表页，重新加载列表并清除选中的CVE
          selectedCVE.value = null;
          loadCVEs();
        }
      } catch (error) {
        console.error('删除CVE失败:', error);
        alert('删除CVE失败，请稍后重试');
      }
    };

    // 格式化翻译说明
    const formatTranslationNote = (note) => {
      if (!note) return '';

      // 如果是字符串，直接返回
      if (typeof note === 'string') {
        return note;
      }

      // 如果是对象，尝试格式化为可读的文本
      try {
        let formattedNote = '';

        if (typeof note === 'object') {
          // 遍历对象的每个属性
          Object.keys(note).forEach(key => {
            const value = note[key];
            formattedNote += `【${key}】\n`;

            if (typeof value === 'object') {
              // 如果值也是对象，进一步展开
              Object.keys(value).forEach(subKey => {
                formattedNote += `  • ${subKey}: ${value[subKey]}\n`;
              });
            } else {
              formattedNote += `  ${value}\n`;
            }
            formattedNote += '\n';
          });
        }

        return formattedNote.trim();
      } catch (error) {
        console.error('格式化翻译说明失败:', error);
        // 如果格式化失败，尝试转换为JSON字符串
        try {
          return JSON.stringify(note, null, 2);
        } catch (jsonError) {
          return String(note);
        }
      }
    };

    // 获取CVSS颜色类
    const getCVSSClasses = (cvssScore) => {
      if (!cvssScore) {
        return 'bg-gray-100 text-gray-800';
      }

      if (cvssScore >= 9) {
        return 'bg-red-300 text-red-800';
      } else if (cvssScore >= 7) {
        return 'bg-red-100 text-red-800';
      } else if (cvssScore >= 4) {
        return 'bg-yellow-100 text-yellow-800';
      } else {
        return 'bg-green-100 text-green-800';
      }
    };

    // 获取状态颜色类
    const getStatusClasses = (status) => {
      switch (status) {
        case 'watching':
          return 'bg-yellow-100 text-yellow-800';
        case 'ignored':
          return 'bg-gray-100 text-gray-800';
        case 'processed':
          return 'bg-green-100 text-green-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    };

    // 获取状态文本
    const getStatusText = (status) => {
      switch (status) {
        case 'watching':
          return '已关注';
        case 'ignored':
          return '已忽略';
        case 'processed':
          return '已解决';
        default:
          return '未知';
      }
    };

    // 格式化日期
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString();
    };

    // 格式化日期时间
    const formatDateTime = (dateString) => {
      return new Date(dateString).toLocaleString();
    };

    return {
      cves,
      loading,
      selectedCVE,
      githubRepos,
      loadingRepos,
      ruleNames,
      filters,
      pagination,
      totalPages,
      loadCVEs,
      selectCVE,
      updateCVEStatus,
      resetFilters,
      changePage,
      handleSearch,
      translateCVE,
      deleteCVE,
      formatTranslationNote,
      getCVSSClasses,
      getStatusClasses,
      getStatusText,
      formatDate,
      formatDateTime,
      from,
      translating // 添加翻译状态变量
    };
  },
  template: `
    <div class="flex flex-col h-full">
      <!-- 详情页模式 -->
      <div v-if="id" class="mb-6 flex justify-between items-center">
        <div class="flex items-center">
          <router-link :to="from === 'dashboard' ? '/' : '/cves'" class="mr-3 text-blue-600 hover:text-blue-800 flex items-center">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            {{ from === 'dashboard' ? '返回仪表盘' : '返回列表' }}
          </router-link>
          <h1 class="text-2xl font-bold text-gray-800">CVE 详情</h1>
        </div>
      </div>

      <!-- 列表页模式 -->
      <div v-else class="mb-6 flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-800">CVE 管理</h1>
        <button @click="loadCVEs" class="btn-primary flex items-center">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          刷新数据
        </button>
      </div>

      <!-- 详情页模式 -->
      <div v-if="id" class="flex flex-1 overflow-hidden">
        <!-- CVE Details Full Page -->
        <Card class="w-full flex flex-col" noPadding>
          <div v-if="loading" class="p-6 text-center text-gray-500 flex-1 flex items-center justify-center">
            <div>
              <svg class="w-12 h-12 mx-auto text-gray-300 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              <p class="mt-2">加载中...</p>
            </div>
          </div>

          <div v-else-if="!selectedCVE" class="p-6 text-center text-gray-500 flex-1 flex items-center justify-center">
            <div>
              <svg class="w-12 h-12 mx-auto text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <p class="mt-2">未找到CVE详情</p>
            </div>
          </div>

          <div v-else class="flex flex-col h-full">
            <div class="p-6 border-b border-gray-100">
              <div class="flex justify-between items-start">
                <div>
                  <!-- 翻译状态调试: {{ selectedCVE.translated }} ({{ typeof selectedCVE.translated }}) -->
                  <div class="flex items-center">
                    <h3 class="text-xl font-semibold text-gray-800">{{ selectedCVE.cve_id }}</h3>
                    <button
                      v-if="selectedCVE && selectedCVE.title"
                      @click="translateCVE(selectedCVE.id)"
                      :disabled="translating"
                      class="ml-3 px-3 py-1 text-sm rounded-md flex items-center"
                      :class="translating ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : 'bg-blue-100 text-blue-800 hover:bg-blue-200'"
                    >
                      <svg v-if="translating" class="w-4 h-4 mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                      </svg>
                      <svg v-else class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                      </svg>
                      {{ translating ? '正在翻译...' : (selectedCVE.title_zh || selectedCVE.description_zh ? '重新翻译' : '翻译') }}
                    </button>
                  </div>
                  <div class="text-md text-gray-600 mt-1">
                    <p><span class="text-gray-500 text-sm">[EN]</span> {{ selectedCVE.title }}</p>
                    <p v-if="selectedCVE.title_zh" class="mt-1"><span class="text-gray-500 text-sm">[中]</span> {{ selectedCVE.title_zh }}</p>
                    <div v-if="selectedCVE.title_zh_note" class="mt-2 text-xs text-gray-500 bg-gray-50 p-2 rounded">
                      <p><strong>标题翻译说明：</strong></p>
                      <pre class="whitespace-pre-wrap mt-1">{{ formatTranslationNote(selectedCVE.title_zh_note) }}</pre>
                    </div>
                  </div>
                </div>
                <span class="px-3 py-1 text-sm rounded-full whitespace-nowrap" :class="getCVSSClasses(selectedCVE.cvss_score)">
                  CVSS: {{ selectedCVE.cvss_score ? selectedCVE.cvss_score.toFixed(1) : 'N/A' }}
                </span>
              </div>

              <div class="mt-6 flex space-x-3">
                <button
                  @click="updateCVEStatus(selectedCVE.id, 'watching')"
                  class="px-4 py-2 rounded-md text-sm"
                  :class="selectedCVE.status === 'watching' ? 'bg-yellow-500 text-white' : 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'"
                >
                  {{ selectedCVE.status === 'watching' ? '已关注' : '关注' }}
                </button>
                <button
                  @click="updateCVEStatus(selectedCVE.id, 'ignored')"
                  class="px-4 py-2 rounded-md text-sm"
                  :class="selectedCVE.status === 'ignored' ? 'bg-gray-500 text-white' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'"
                >
                  {{ selectedCVE.status === 'ignored' ? '已忽略' : '忽略' }}
                </button>
                <button
                  @click="updateCVEStatus(selectedCVE.id, 'processed')"
                  class="px-4 py-2 rounded-md text-sm"
                  :class="selectedCVE.status === 'processed' ? 'bg-green-500 text-white' : 'bg-green-100 text-green-800 hover:bg-green-200'"
                >
                  {{ selectedCVE.status === 'processed' ? '已解决' : '解决' }}
                </button>
                <button
                  @click="deleteCVE(selectedCVE.id)"
                  class="px-4 py-2 rounded-md text-sm bg-red-100 text-red-800 hover:bg-red-200"
                >
                  删除
                </button>
              </div>
            </div>

            <div class="p-6 grid grid-cols-1 md:grid-cols-3 gap-6 flex-1 overflow-auto">
              <!-- 左侧信息 -->
              <div class="md:col-span-2 space-y-6">
                <div>
                  <h4 class="text-lg font-medium text-gray-700 mb-2">描述</h4>
                  <div class="text-gray-600">
                    <div>
                      <p><span class="text-gray-500 text-sm">[EN]</span> {{ selectedCVE.description }}</p>
                    </div>
                    <div v-if="selectedCVE.description_zh" class="mt-4">
                      <p><span class="text-gray-500 text-sm">[中]</span> {{ selectedCVE.description_zh }}</p>
                      <div v-if="selectedCVE.description_zh_note" class="mt-2 text-xs text-gray-500 bg-gray-50 p-2 rounded">
                        <p><strong>翻译说明：</strong></p>
                        <pre class="whitespace-pre-wrap mt-1">{{ formatTranslationNote(selectedCVE.description_zh_note) }}</pre>
                      </div>
                    </div>
                  </div>
                </div>

                <div v-if="selectedCVE.cvss_vector">
                  <h4 class="text-lg font-medium text-gray-700 mb-2">CVSS向量</h4>
                  <div class="bg-gray-50 p-3 rounded-md">
                    <p class="text-gray-600 font-mono">{{ selectedCVE.cvss_vector }}</p>
                  </div>
                </div>

                <div v-if="selectedCVE.weaknesses">
                  <h4 class="text-lg font-medium text-gray-700 mb-2">弱点</h4>
                  <div class="flex flex-wrap gap-2">
                    <span
                      v-for="(weakness, index) in selectedCVE.weaknesses.split(', ')"
                      :key="index"
                      class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                    >
                      {{ weakness }}
                    </span>
                  </div>
                </div>

                <!-- GitHub仓库 -->
                <div>
                  <h4 class="text-lg font-medium text-gray-700 mb-2">相关GitHub仓库</h4>
                  <div v-if="loadingRepos" class="text-center text-gray-500 py-4">
                    <svg class="w-8 h-8 mx-auto text-gray-300 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    <p class="mt-2">加载中...</p>
                  </div>
                  <div v-else-if="githubRepos.length === 0" class="text-center text-gray-500 py-4">
                    暂无相关仓库
                  </div>
                  <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div v-for="repo in githubRepos" :key="repo.id" class="p-4 border border-gray-200 rounded-lg">
                      <div class="flex justify-between items-start">
                        <div>
                          <a :href="repo.repo_url" target="_blank" class="text-md font-medium text-blue-600 hover:text-blue-800">
                            {{ repo.repo_name }}
                          </a>
                          <p class="text-sm text-gray-500 mt-1">{{ repo.description }}</p>
                        </div>
                        <span class="flex items-center text-sm text-gray-600">
                          <svg class="w-4 h-4 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                          </svg>
                          {{ repo.stars_count }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 右侧信息 -->
              <div class="space-y-6">
                <div class="bg-gray-50 p-4 rounded-lg">
                  <h4 class="text-lg font-medium text-gray-700 mb-4">基本信息</h4>

                  <div class="space-y-3">
                    <div>
                      <h5 class="text-sm font-medium text-gray-500">状态</h5>
                      <p class="text-gray-800">
                        <span class="inline-block px-2 py-1 text-xs rounded-full mt-1" :class="getStatusClasses(selectedCVE.status)">
                          {{ getStatusText(selectedCVE.status) }}
                        </span>
                      </p>
                    </div>

                    <div>
                      <h5 class="text-sm font-medium text-gray-500">厂商</h5>
                      <p class="text-gray-800">{{ selectedCVE.vendor || '未知' }}</p>
                    </div>

                    <div>
                      <h5 class="text-sm font-medium text-gray-500">产品</h5>
                      <p class="text-gray-800">{{ selectedCVE.product || '未知' }}</p>
                    </div>

                    <div>
                      <h5 class="text-sm font-medium text-gray-500">发布日期</h5>
                      <p class="text-gray-800">{{ formatDate(selectedCVE.published_at) }}</p>
                    </div>

                    <div>
                      <h5 class="text-sm font-medium text-gray-500">更新日期</h5>
                      <p class="text-gray-800">{{ formatDate(selectedCVE.modified_at) }}</p>
                    </div>

                    <div>
                      <h5 class="text-sm font-medium text-gray-500">记录时间</h5>
                      <p class="text-gray-800">{{ formatDateTime(selectedCVE.created_at) }}</p>
                    </div>

                    <div>
                      <h5 class="text-sm font-medium text-gray-500">最后更新时间</h5>
                      <p class="text-gray-800">{{ formatDateTime(selectedCVE.updated_at) }}</p>
                    </div>

                    <div>
                      <h5 class="text-sm font-medium text-gray-500">匹配规则</h5>
                      <div v-if="selectedCVE.matched_rules" class="mt-1">
                        <div v-for="(ruleId, index) in selectedCVE.matched_rules.split(',')" :key="index" class="inline-block px-2 py-1 mr-2 mb-2 text-xs rounded-full bg-blue-100 text-blue-800">
                          {{ ruleNames[ruleId] ? ruleNames[ruleId] : '规则ID: ' + ruleId }}
                        </div>
                      </div>
                      <p v-else class="text-gray-800">无</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>

      <!-- 列表页模式 -->
      <div v-else>
        <!-- Filters -->
        <Card class="mb-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
              <input
                v-model="filters.search"
                type="text"
                placeholder="CVE ID, 标题或描述"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
              <select
                v-model="filters.status"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">全部</option>
                <option value="watching">已关注</option>
                <option value="ignored">已忽略</option>
                <option value="processed">已解决</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">最低CVSS评分</label>
              <select
                v-model="filters.min_cvss"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="0">全部</option>
                <option value="9">致命 (≥9.0)</option>
                <option value="7">高危 (≥7.0)</option>
                <option value="4">中危 (≥4.0)</option>
                <option value="1">低危 (≥1.0)</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">厂商</label>
              <input
                v-model="filters.vendor"
                type="text"
                placeholder="厂商名称"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">产品</label>
              <input
                v-model="filters.product"
                type="text"
                placeholder="产品名称"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
            </div>
          </div>

          <div class="mt-4 flex justify-end space-x-3">
            <button @click="handleSearch" class="btn-primary px-4 py-2 text-sm">
              <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
              查询
            </button>
            <button @click="resetFilters" class="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded">
              重置筛选
            </button>
          </div>
        </Card>

        <!-- Main Content -->
        <div class="flex flex-1 space-x-6 overflow-hidden">
        <!-- CVE List -->
        <Card class="w-2/3 flex flex-col" noPadding>
          <div class="p-4 border-b border-gray-100 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-800">CVE 列表</h3>
            <span class="text-sm text-gray-500">共 {{ pagination.total }} 条记录</span>
          </div>

          <div class="flex-1 overflow-auto">
            <div v-if="loading" class="p-6 text-center text-gray-500">
              加载中...
            </div>
            <div v-else-if="cves.length === 0" class="p-6 text-center text-gray-500">
              暂无数据
            </div>
            <table v-else class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CVE ID</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标题</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CVSS</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">厂商/产品</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布日期</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">记录时间</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后更新</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr
                  v-for="cve in cves"
                  :key="cve.id"
                  @click="selectCVE(cve)"
                  class="hover:bg-gray-50 cursor-pointer"
                  :class="{ 'bg-blue-50': selectedCVE && selectedCVE.id === cve.id }"
                >
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ cve.cve_id }}</div>
                  </td>
                  <td class="px-6 py-4">
                    <div class="text-sm text-gray-900 line-clamp-2 max-w-md">{{ cve.title_zh || cve.title }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 py-1 text-xs rounded-full" :class="getCVSSClasses(cve.cvss_score)">
                      {{ cve.cvss_score ? cve.cvss_score.toFixed(1) : 'N/A' }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ cve.vendor }}</div>
                    <div class="text-xs text-gray-500">{{ cve.product }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 py-1 text-xs rounded-full" :class="getStatusClasses(cve.status)">
                      {{ getStatusText(cve.status) }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(cve.published_at) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDateTime(cve.created_at) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDateTime(cve.updated_at) }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div class="p-4 border-t border-gray-100 flex justify-between items-center">
            <div class="text-sm text-gray-500">
              显示 {{ (pagination.page - 1) * pagination.page_size + 1 }} -
              {{ Math.min(pagination.page * pagination.page_size, pagination.total) }}
              条，共 {{ pagination.total }} 条
            </div>
            <div class="flex space-x-1">
              <button
                @click="changePage(pagination.page - 1)"
                :disabled="pagination.page === 1"
                class="px-3 py-1 rounded border"
                :class="pagination.page === 1 ? 'text-gray-400 border-gray-200' : 'text-gray-600 border-gray-300 hover:bg-gray-50'"
              >
                上一页
              </button>
              <button
                v-for="page in totalPages"
                :key="page"
                @click="changePage(page)"
                class="px-3 py-1 rounded border"
                :class="page === pagination.page ? 'bg-blue-500 text-white border-blue-500' : 'text-gray-600 border-gray-300 hover:bg-gray-50'"
              >
                {{ page }}
              </button>
              <button
                @click="changePage(pagination.page + 1)"
                :disabled="pagination.page === totalPages"
                class="px-3 py-1 rounded border"
                :class="pagination.page === totalPages ? 'text-gray-400 border-gray-200' : 'text-gray-600 border-gray-300 hover:bg-gray-50'"
              >
                下一页
              </button>
            </div>
          </div>
        </Card>

        <!-- CVE Details -->
        <Card class="w-1/3 flex flex-col" title="CVE 详情" noPadding>
          <div v-if="!selectedCVE" class="p-6 text-center text-gray-500 flex-1 flex items-center justify-center">
            <div>
              <svg class="w-12 h-12 mx-auto text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <p class="mt-2">选择一个CVE查看详情</p>
            </div>
          </div>

          <div v-else class="flex flex-col h-full">
            <div class="p-6 border-b border-gray-100">
              <div class="flex justify-between items-start">
                <div>
                  <div class="flex items-center">
                    <h3 class="text-lg font-semibold text-gray-800">{{ selectedCVE.cve_id }}</h3>
                    <button
                      v-if="selectedCVE && selectedCVE.title"
                      @click="translateCVE(selectedCVE.id)"
                      :disabled="translating"
                      class="ml-3 px-3 py-1 text-xs rounded-md flex items-center"
                      :class="translating ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : 'bg-blue-100 text-blue-800 hover:bg-blue-200'"
                    >
                      <svg v-if="translating" class="w-4 h-4 mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                      </svg>
                      <svg v-else class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                      </svg>
                      {{ translating ? '正在翻译...' : (selectedCVE.title_zh || selectedCVE.description_zh ? '重新翻译' : '翻译') }}
                    </button>
                  </div>
                  <div class="text-sm text-gray-500 mt-1">
                    <p><span class="text-gray-500 text-xs">[EN]</span> {{ selectedCVE.title }}</p>
                    <p v-if="selectedCVE.title_zh" class="mt-1"><span class="text-gray-500 text-xs">[中]</span> {{ selectedCVE.title_zh }}</p>
                    <div v-if="selectedCVE.title_zh_note" class="mt-2 text-xs text-gray-500 bg-gray-50 p-2 rounded">
                      <p><strong>标题翻译说明：</strong></p>
                      <pre class="whitespace-pre-wrap mt-1">{{ formatTranslationNote(selectedCVE.title_zh_note) }}</pre>
                    </div>
                  </div>
                </div>
                <span class="px-2 py-1 text-xs rounded-full whitespace-nowrap" :class="getCVSSClasses(selectedCVE.cvss_score)">
                  CVSS: {{ selectedCVE.cvss_score ? selectedCVE.cvss_score.toFixed(1) : 'N/A' }}
                </span>
              </div>

              <div class="mt-4 flex space-x-2">
                <button
                  @click="updateCVEStatus(selectedCVE.id, 'watching')"
                  class="px-3 py-1 text-xs rounded-full"
                  :class="selectedCVE.status === 'watching' ? 'bg-yellow-500 text-white' : 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'"
                >
                  {{ selectedCVE.status === 'watching' ? '已关注' : '关注' }}
                </button>
                <button
                  @click="updateCVEStatus(selectedCVE.id, 'ignored')"
                  class="px-3 py-1 text-xs rounded-full"
                  :class="selectedCVE.status === 'ignored' ? 'bg-gray-500 text-white' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'"
                >
                  {{ selectedCVE.status === 'ignored' ? '已忽略' : '忽略' }}
                </button>
                <button
                  @click="updateCVEStatus(selectedCVE.id, 'processed')"
                  class="px-3 py-1 text-xs rounded-full"
                  :class="selectedCVE.status === 'processed' ? 'bg-green-500 text-white' : 'bg-green-100 text-green-800 hover:bg-green-200'"
                >
                  {{ selectedCVE.status === 'processed' ? '已解决' : '解决' }}
                </button>
                <button
                  @click="deleteCVE(selectedCVE.id)"
                  class="px-3 py-1 text-xs rounded-full bg-red-100 text-red-800 hover:bg-red-200"
                >
                  删除
                </button>
              </div>
            </div>

            <div class="p-6 flex-1 overflow-auto">
              <div class="space-y-4">
                <div>
                  <h4 class="text-sm font-medium text-gray-700">描述</h4>
                  <div class="mt-1 text-sm text-gray-600">
                    <p><span class="text-gray-500 text-xs">[EN]</span> {{ selectedCVE.description }}</p>
                    <div v-if="selectedCVE.description_zh" class="mt-2">
                      <p><span class="text-gray-500 text-xs">[中]</span> {{ selectedCVE.description_zh }}</p>
                      <div v-if="selectedCVE.description_zh_note" class="mt-2 text-xs text-gray-500 bg-gray-50 p-2 rounded">
                        <p><strong>翻译说明：</strong></p>
                        <pre class="whitespace-pre-wrap mt-1">{{ formatTranslationNote(selectedCVE.description_zh_note) }}</pre>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">厂商</h4>
                    <p class="mt-1 text-sm text-gray-600">{{ selectedCVE.vendor || '未知' }}</p>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">产品</h4>
                    <p class="mt-1 text-sm text-gray-600">{{ selectedCVE.product || '未知' }}</p>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">发布日期</h4>
                    <p class="mt-1 text-sm text-gray-600">{{ formatDate(selectedCVE.published_at) }}</p>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">更新日期</h4>
                    <p class="mt-1 text-sm text-gray-600">{{ formatDate(selectedCVE.modified_at) }}</p>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">记录时间</h4>
                    <p class="mt-1 text-sm text-gray-600">{{ formatDateTime(selectedCVE.created_at) }}</p>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">最后更新时间</h4>
                    <p class="mt-1 text-sm text-gray-600">{{ formatDateTime(selectedCVE.updated_at) }}</p>
                  </div>
                </div>

                <div v-if="selectedCVE.cvss_vector">
                  <h4 class="text-sm font-medium text-gray-700">CVSS向量</h4>
                  <p class="mt-1 text-sm text-gray-600 font-mono">{{ selectedCVE.cvss_vector }}</p>
                </div>

                <div v-if="selectedCVE.weaknesses">
                  <h4 class="text-sm font-medium text-gray-700">弱点</h4>
                  <p class="mt-1 text-sm text-gray-600">{{ selectedCVE.weaknesses }}</p>
                </div>

                <div>
                  <h4 class="text-sm font-medium text-gray-700">匹配规则</h4>
                  <div v-if="selectedCVE.matched_rules" class="mt-1">
                    <div v-for="(ruleId, index) in selectedCVE.matched_rules.split(',')" :key="index" class="inline-block px-2 py-1 mr-2 mb-2 text-xs rounded-full bg-blue-100 text-blue-800">
                      {{ ruleNames[ruleId] ? ruleNames[ruleId] : '规则ID: ' + ruleId }}
                    </div>
                  </div>
                  <p v-else class="mt-1 text-sm text-gray-600">无</p>
                </div>

                <!-- GitHub Repos -->
                <div>
                  <h4 class="text-sm font-medium text-gray-700 mb-2">相关GitHub仓库</h4>
                  <div v-if="loadingRepos" class="text-center text-gray-500 py-4">
                    加载中...
                  </div>
                  <div v-else-if="githubRepos.length === 0" class="text-center text-gray-500 py-4">
                    暂无相关仓库
                  </div>
                  <div v-else class="space-y-3">
                    <div v-for="repo in githubRepos" :key="repo.id" class="p-3 border border-gray-200 rounded-lg">
                      <div class="flex justify-between items-start">
                        <div>
                          <a :href="repo.repo_url" target="_blank" class="text-sm font-medium text-blue-600 hover:text-blue-800">
                            {{ repo.repo_name }}
                          </a>
                          <p class="text-xs text-gray-500 mt-1">{{ repo.description }}</p>
                        </div>
                        <span class="flex items-center text-xs text-gray-600">
                          <svg class="w-4 h-4 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                          </svg>
                          {{ repo.stars_count }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  `
};
