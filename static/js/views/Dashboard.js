import Card from '../components/common/Card.js';
import cveService from '../services/cveService.js';
import githubService from '../services/githubService.js';
import configService from '../services/configService.js';

export default {
  components: {
    Card
  },
  directives: {
    tooltip: {
      mounted(el, binding, vnode) {
        const button = el.querySelector('button');
        const tooltip = el.querySelector('div.fixed');

        if (button && tooltip) {
          button.addEventListener('mouseenter', () => {
            // 获取按钮的位置
            const rect = button.getBoundingClientRect();
            // 设置悬浮窗口的位置
            tooltip.style.top = `${rect.bottom + 5}px`;
            tooltip.style.left = `${rect.left}px`;
          });
        }
      }
    }
  },
  setup() {
    const { ref, onMounted, computed } = Vue;

    // 状态
    const stats = ref({
      totalCVEs: 0,
      criticalCVEs: 0,
      watchingCVEs: 0,
      totalRules: 0,
      totalRepos: 0
    });

    const recentCVEs = ref([]);
    const monitorHistory = ref([]);
    const loading = ref(true);

    // 计算属性
    const lastCVERunTime = computed(() => {
      const history = monitorHistory.value.find(h => h.type === 'cve');
      return history ? new Date(history.run_time).toLocaleString() : '未知';
    });

    const lastGitHubRunTime = computed(() => {
      const history = monitorHistory.value.find(h => h.type === 'github');
      return history ? new Date(history.run_time).toLocaleString() : '未知';
    });

    // 方法
    const loadDashboardData = async () => {
      loading.value = true;
      try {
        // 获取CVE统计数据
        const statsResponse = await cveService.getStats();
        if (statsResponse.data) {
          stats.value.totalCVEs = statsResponse.data.total;
          stats.value.criticalCVEs = statsResponse.data.critical;
          stats.value.watchingCVEs = statsResponse.data.watching;
          recentCVEs.value = statsResponse.data.recent;
        }

        // 获取监控历史
        const historyResponse = await cveService.getMonitorHistory();
        if (historyResponse.data) {
          monitorHistory.value = historyResponse.data;
        }

        // 获取GitHub仓库数据
        const repoResponse = await githubService.getRepos({
          page: 1,
          page_size: 10 // 只需要获取总数
        });
        if (repoResponse.data) {
          stats.value.totalRepos = repoResponse.data.pagination.total;
        }

      } catch (error) {
        console.error('加载仪表盘数据失败:', error);
      } finally {
        loading.value = false;
      }
    };

    const triggerCVEMonitor = async () => {
      try {
        await cveService.triggerCVEMonitor();
        alert('CVE监控已触发，请稍后刷新查看结果');
        loadDashboardData();
      } catch (error) {
        console.error('触发CVE监控失败:', error);
      }
    };

    const triggerGitHubMonitor = async () => {
      try {
        await githubService.triggerGitHubMonitor();
        alert('GitHub监控已触发，请稍后刷新查看结果');
        loadDashboardData();
      } catch (error) {
        console.error('触发GitHub监控失败:', error);
      }
    };

    // 获取CVSS颜色类
    const getCVSSClasses = (cvssScore) => {
      if (!cvssScore) {
        return 'bg-gray-100 text-gray-800';
      }

      if (cvssScore >= 9) {
        return 'bg-red-300 text-red-800';
      } else if (cvssScore >= 7) {
        return 'bg-red-100 text-red-800';
      } else if (cvssScore >= 4) {
        return 'bg-yellow-100 text-yellow-800';
      } else {
        return 'bg-green-100 text-green-800';
      }
    };

    // 生命周期钩子
    onMounted(() => {
      loadDashboardData();
    });

    return {
      stats,
      recentCVEs,
      monitorHistory,
      loading,
      lastCVERunTime,
      lastGitHubRunTime,
      triggerCVEMonitor,
      triggerGitHubMonitor,
      loadDashboardData,
      getCVSSClasses
    };
  },
  template: `
    <div>
      <div class="mb-6 flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-800">系统概览</h1>
        <div class="flex space-x-3">
          <button @click="triggerCVEMonitor" class="btn-primary flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            运行CVE监控
          </button>
          <button @click="triggerGitHubMonitor" class="btn-primary flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            运行GitHub监控
          </button>
        </div>
      </div>

      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- Total CVEs -->
        <div class="card p-6 flex items-center">
          <div class="rounded-full p-3 bg-blue-100 mr-4">
            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">总CVE数量</p>
            <p class="text-2xl font-bold text-gray-800">{{ stats.totalCVEs }}</p>
          </div>
        </div>

        <!-- Critical CVEs -->
        <div class="card p-6 flex items-center">
          <div class="rounded-full p-3 bg-red-200 mr-4">
            <svg class="w-8 h-8 text-red-800" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">致命CVE</p>
            <p class="text-2xl font-bold text-gray-800">{{ stats.criticalCVEs }}</p>
          </div>
        </div>

        <!-- Watching CVEs -->
        <div class="card p-6 flex items-center">
          <div class="rounded-full p-3 bg-yellow-100 mr-4">
            <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">关注中CVE</p>
            <p class="text-2xl font-bold text-gray-800">{{ stats.watchingCVEs }}</p>
          </div>
        </div>

        <!-- GitHub Repos -->
        <div class="card p-6 flex items-center">
          <div class="rounded-full p-3 bg-purple-100 mr-4">
            <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
            </svg>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">GitHub仓库</p>
            <p class="text-2xl font-bold text-gray-800">{{ stats.totalRepos }}</p>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Recent CVEs -->
        <Card title="最近CVE" subtitle="最新发现的安全漏洞" class="lg:col-span-2">
          <div v-if="loading" class="py-8 text-center text-gray-500">
            加载中...
          </div>
          <div v-else-if="recentCVEs.length === 0" class="py-8 text-center text-gray-500">
            暂无数据
          </div>
          <div v-else class="divide-y divide-gray-100">
            <div v-for="cve in recentCVEs" :key="cve.id" class="py-4">
              <div class="flex justify-between items-start space-x-4">
                <div class="flex-1 min-w-0">
                  <h4 class="text-md font-medium text-gray-800">{{ cve.cve_id }}</h4>
                  <div class="flex items-center mt-1">
                    <p class="text-sm font-medium text-gray-700">{{ cve.title_zh || cve.title }}</p>
                    <div v-if="cve.title_zh && cve.title" class="relative ml-1 group" v-tooltip>
                      <button class="text-gray-400 hover:text-gray-600 focus:outline-none">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path d="M6 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm12 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm-6 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"></path>
                        </svg>
                      </button>
                      <div class="fixed z-50 mt-2 w-80 p-2 bg-white rounded-md shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200" style="top: auto; left: auto;">
                        <p class="text-xs text-gray-600">{{ cve.title }}</p>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center mt-1">
                    <p class="text-sm text-gray-600 line-clamp-2 overflow-hidden text-ellipsis">{{ cve.description_zh || cve.description }}</p>
                    <div v-if="cve.description_zh && cve.description" class="relative ml-1 group" v-tooltip>
                      <button class="text-gray-400 hover:text-gray-600 focus:outline-none">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path d="M6 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm12 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm-6 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"></path>
                        </svg>
                      </button>
                      <div class="fixed z-50 mt-2 w-80 p-2 bg-white rounded-md shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200" style="top: auto; left: auto;">
                        <p class="text-xs text-gray-600 max-h-40 overflow-y-auto">{{ cve.description }}</p>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center mt-2">
                    <span class="text-xs px-2 py-1 rounded-full" :class="getCVSSClasses(cve.cvss_score)">
                      CVSS: {{ cve.cvss_score.toFixed(1) }}
                    </span>
                    <span v-if="cve.vendor" class="text-xs px-2 py-1 rounded-full ml-2 bg-gray-100 text-gray-800">
                      {{ cve.vendor }}
                    </span>
                    <span v-if="cve.product" class="text-xs px-2 py-1 rounded-full ml-2 bg-blue-50 text-blue-700">
                      {{ cve.product }}
                    </span>
                    <span class="text-xs text-gray-500 ml-2">
                      {{ new Date(cve.published_at).toLocaleDateString() }}
                    </span>
                  </div>
                </div>
                <router-link :to="{ path: '/cves/' + cve.id, query: { from: 'dashboard' } }" class="text-blue-600 hover:text-blue-800 text-sm whitespace-nowrap flex-shrink-0 ml-2">
                  查看详情
                </router-link>
              </div>
            </div>
          </div>
          <div class="mt-4 text-right">
            <router-link to="/cves" class="text-blue-600 hover:text-blue-800 text-sm">
              查看全部 →
            </router-link>
          </div>
        </Card>

        <!-- Monitor Status -->
        <Card title="监控状态" subtitle="系统监控运行情况">
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <div>
                <h4 class="text-md font-medium text-gray-800">CVE监控</h4>
                <p class="text-sm text-gray-500">上次运行: {{ lastCVERunTime }}</p>
              </div>
              <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                正常
              </span>
            </div>

            <div class="flex justify-between items-center">
              <div>
                <h4 class="text-md font-medium text-gray-800">GitHub监控</h4>
                <p class="text-sm text-gray-500">上次运行: {{ lastGitHubRunTime }}</p>
              </div>
              <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                正常
              </span>
            </div>

            <div class="border-t border-gray-100 pt-4 mt-4">
              <h4 class="text-md font-medium text-gray-800 mb-3">最近监控历史</h4>
              <div v-if="monitorHistory.length === 0" class="text-center text-gray-500 py-4">
                暂无数据
              </div>
              <div v-else class="space-y-3">
                <div v-for="(history, index) in monitorHistory.slice(0, 5)" :key="index" class="flex justify-between items-center">
                  <div>
                    <p class="text-sm font-medium text-gray-700">
                      {{ history.type === 'cve' ? 'CVE监控' : 'GitHub监控' }}
                    </p>
                    <p class="text-xs text-gray-500">
                      {{ new Date(history.run_time).toLocaleString() }}
                    </p>
                  </div>
                  <span class="px-2 py-1 text-xs rounded-full" :class="{
                    'bg-green-100 text-green-800': history.status === 'success',
                    'bg-red-100 text-red-800': history.status === 'failed'
                  }">
                    {{ history.status === 'success' ? '成功' : '失败' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  `
};
