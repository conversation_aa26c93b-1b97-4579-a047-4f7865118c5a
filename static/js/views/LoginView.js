import authService from '../services/authService.js';

export default {
  setup() {
    const { ref, reactive } = Vue;
    
    // 响应式数据
    const loading = ref(false);
    const form = reactive({
      username: '',
      password: ''
    });
    const error = ref('');

    // 登录方法
    const login = async () => {
      if (!form.username || !form.password) {
        error.value = '请输入用户名和密码';
        return;
      }

      loading.value = true;
      error.value = '';

      try {
        await authService.login(form.username, form.password);
        
        // 登录成功，跳转到仪表盘
        window.location.href = '/';
      } catch (err) {
        console.error('登录失败:', err);
        if (err.response && err.response.data && err.response.data.message) {
          error.value = err.response.data.message;
        } else {
          error.value = '登录失败，请检查用户名和密码';
        }
      } finally {
        loading.value = false;
      }
    };

    // 处理回车键登录
    const handleKeyPress = (event) => {
      if (event.key === 'Enter') {
        login();
      }
    };

    return {
      form,
      loading,
      error,
      login,
      handleKeyPress
    };
  },
  template: `
    <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        <div>
          <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
            CVE监控系统
          </h2>
          <p class="mt-2 text-center text-sm text-gray-600">
            请登录您的账户
          </p>
        </div>
        <form class="mt-8 space-y-6" @submit.prevent="login">
          <div class="rounded-md shadow-sm -space-y-px">
            <div>
              <label for="username" class="sr-only">用户名</label>
              <input
                id="username"
                name="username"
                type="text"
                required
                v-model="form.username"
                @keypress="handleKeyPress"
                class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="用户名"
              />
            </div>
            <div>
              <label for="password" class="sr-only">密码</label>
              <input
                id="password"
                name="password"
                type="password"
                required
                v-model="form.password"
                @keypress="handleKeyPress"
                class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="密码"
              />
            </div>
          </div>

          <div v-if="error" class="text-red-600 text-sm text-center">
            {{ error }}
          </div>

          <div>
            <button
              type="submit"
              :disabled="loading"
              class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="loading" class="absolute left-0 inset-y-0 flex items-center pl-3">
                <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </span>
              {{ loading ? '登录中...' : '登录' }}
            </button>
          </div>

          <div class="text-center text-sm text-gray-600">
            <p>默认账户：admin / admin123</p>
          </div>
        </form>
      </div>
    </div>
  `
};
