import Card from '../components/common/Card.js';
import ruleService from '../services/ruleService.js';

export default {
  components: {
    Card
  },
  setup() {
    const { ref, reactive, computed, onMounted } = Vue;

    // 状态
    const rules = ref([]);
    const loading = ref(true);
    const showForm = ref(false);
    const editingRule = ref(null);
    const errorMessage = ref(''); // 错误提示消息

    // 分页状态
    const pagination = reactive({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });

    // 搜索状态
    const searchQuery = ref('');

    // 表单数据
    const ruleForm = reactive({
      name: '',
      keywords: '',
      vendor_name: '',
      product_name: '',
      min_cvss_score: 0,
      enabled: true
    });

    // 方法
    const loadRules = async () => {
      loading.value = true;
      try {
        const params = {
          page: pagination.page,
          page_size: pagination.pageSize,
          search: searchQuery.value
        };
        console.log('发送请求参数:', params);
        const response = await ruleService.getRulesPaged(params);
        console.log('API响应:', response);
        console.log('响应数据:', response.data);

        if (response.data && response.data.code === 200) {
          console.log('解析前 - response.data.data:', response.data.data);
          console.log('解析前 - response.data.pagination:', response.data.pagination);

          rules.value = response.data.data || [];
          pagination.total = response.data.pagination.total || 0;
          pagination.totalPages = response.data.pagination.total_pages || 0;

          console.log('解析后 - rules.value:', rules.value);
          console.log('解析后 - pagination:', pagination);
        } else {
          console.log('响应状态不是200或数据为空');
          // 处理错误情况
          rules.value = [];
          pagination.total = 0;
          pagination.totalPages = 0;
        }
      } catch (error) {
        console.error('加载规则数据失败:', error);
        // 错误时重置数据
        rules.value = [];
        pagination.total = 0;
        pagination.totalPages = 0;
      } finally {
        loading.value = false;
      }
    };

    // 搜索规则
    const searchRules = () => {
      pagination.page = 1; // 重置到第一页
      loadRules();
    };

    // 清除搜索
    const clearSearch = () => {
      searchQuery.value = '';
      pagination.page = 1;
      loadRules();
    };

    // 分页方法
    const goToPage = (page) => {
      if (page >= 1 && page <= pagination.totalPages) {
        pagination.page = page;
        loadRules();
      }
    };

    const prevPage = () => {
      if (pagination.page > 1) {
        pagination.page--;
        loadRules();
      }
    };

    const nextPage = () => {
      if (pagination.page < pagination.totalPages) {
        pagination.page++;
        loadRules();
      }
    };

    const createRule = async () => {
      try {
        errorMessage.value = ''; // 清除之前的错误消息
        const response = await ruleService.createRule(ruleForm);
        resetForm();
        showForm.value = false;
        loadRules();
      } catch (error) {
        console.error('创建规则失败:', error);
        // 如果错误消息包含“规则名称已存在”，显示特定的错误提示
        if (error.message && error.message.includes('规则名称已存在')) {
          errorMessage.value = '规则名称已存在，请使用其他名称';
        } else {
          errorMessage.value = '创建规则失败: ' + error.message;
        }
      }
    };

    const updateRule = async () => {
      try {
        errorMessage.value = ''; // 清除之前的错误消息
        await ruleService.updateRule(editingRule.value.id, ruleForm);
        resetForm();
        showForm.value = false;
        loadRules();
      } catch (error) {
        console.error('更新规则失败:', error);
        // 如果错误消息包含“规则名称已存在”，显示特定的错误提示
        if (error.message && error.message.includes('规则名称已存在')) {
          errorMessage.value = '规则名称已存在，请使用其他名称';
        } else {
          errorMessage.value = '更新规则失败: ' + error.message;
        }
      }
    };

    const deleteRule = async (id) => {
      if (!confirm('确定要删除这条规则吗？')) {
        return;
      }

      try {
        await ruleService.deleteRule(id);
        loadRules();
      } catch (error) {
        console.error('删除规则失败:', error);
      }
    };

    const editRule = (rule) => {
      editingRule.value = rule;

      // 填充表单
      ruleForm.name = rule.name;
      ruleForm.keywords = rule.keywords;
      ruleForm.vendor_name = rule.vendor_name;
      ruleForm.product_name = rule.product_name;
      ruleForm.min_cvss_score = rule.min_cvss_score;
      ruleForm.enabled = rule.enabled;

      showForm.value = true;
    };

    const resetForm = () => {
      ruleForm.name = '';
      ruleForm.keywords = '';
      ruleForm.vendor_name = '';
      ruleForm.product_name = '';
      ruleForm.min_cvss_score = 0;
      ruleForm.enabled = true;
      editingRule.value = null;
      errorMessage.value = ''; // 清除错误消息
    };

    const toggleRuleStatus = async (rule) => {
      try {
        const updatedRule = { ...rule, enabled: !rule.enabled };
        await ruleService.updateRule(rule.id, updatedRule);
        loadRules();
      } catch (error) {
        console.error('更新规则状态失败:', error);
      }
    };

    // 生命周期钩子
    onMounted(() => {
      loadRules();
    });

    return {
      rules,
      loading,
      showForm,
      editingRule,
      ruleForm,
      errorMessage,
      pagination,
      searchQuery,
      loadRules,
      searchRules,
      clearSearch,
      goToPage,
      prevPage,
      nextPage,
      createRule,
      updateRule,
      deleteRule,
      editRule,
      resetForm,
      toggleRuleStatus
    };
  },
  template: `
    <div>
      <div class="mb-6 flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-800">规则管理</h1>
        <button @click="showForm = true; resetForm()" class="btn-primary flex items-center">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          添加规则
        </button>
      </div>

      <!-- 搜索框 -->
      <div class="mb-4 flex items-center space-x-4">
        <div class="flex-1 relative">
          <input
            v-model="searchQuery"
            @keyup.enter="searchRules"
            type="text"
            placeholder="搜索规则名称、关键字、厂商或产品..."
            class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
          <button
            v-if="searchQuery.trim()"
            @click="clearSearch"
            class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            title="清除搜索"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        <button @click="searchRules" class="btn-primary">
          搜索
        </button>
      </div>

      <!-- Rules List -->
      <Card>
        <div v-if="loading" class="py-8 text-center text-gray-500">
          加载中...
        </div>
        <div v-else-if="rules.length === 0" class="py-8 text-center text-gray-500">
          <div v-if="searchQuery.trim()">
            <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            <p class="text-lg font-medium text-gray-900 mb-2">未找到匹配的规则</p>
            <p class="text-gray-500">尝试调整搜索条件或
              <button @click="clearSearch()" class="text-blue-600 hover:text-blue-800">清除搜索</button>
            </p>
          </div>
          <div v-else>
            <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <p class="text-lg font-medium text-gray-900 mb-2">暂无规则</p>
            <p class="text-gray-500">点击"添加规则"创建第一条规则</p>
          </div>
        </div>
        <div v-else class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关键字</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">厂商</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最低CVSS评分</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">命中数</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="rule in rules" :key="rule.id">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ rule.id }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">{{ rule.name }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ rule.keywords || '-' }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ rule.vendor_name || '-' }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ rule.product_name || '-' }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ rule.min_cvss_score }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                    {{ rule.hit_count || 0 }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="px-2 py-1 text-xs rounded-full cursor-pointer"
                    :class="rule.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                    @click="toggleRuleStatus(rule)"
                  >
                    {{ rule.enabled ? '启用' : '禁用' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <button @click="editRule(rule)" class="text-blue-600 hover:text-blue-900 mr-3">
                    编辑
                  </button>
                  <button @click="deleteRule(rule.id)" class="text-red-600 hover:text-red-900">
                    删除
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页组件 -->
        <div v-if="!loading" class="mt-4 flex items-center justify-between border-t border-gray-200 pt-4">
          <div class="text-sm text-gray-700">
            <span v-if="pagination.total > 0">
              显示第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} - {{ Math.min(pagination.page * pagination.pageSize, pagination.total) }} 条，共 {{ pagination.total }} 条
            </span>
            <span v-else>
              共 0 条记录
            </span>
          </div>
          <div v-if="pagination.totalPages > 1" class="flex items-center space-x-2">
            <button
              @click="prevPage"
              :disabled="pagination.page === 1"
              class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>

            <template v-for="page in Math.min(pagination.totalPages, 5)" :key="page">
              <button
                v-if="page <= pagination.totalPages"
                @click="goToPage(page)"
                :class="page === pagination.page ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'"
                class="px-3 py-1 text-sm border border-gray-300 rounded-md"
              >
                {{ page }}
              </button>
            </template>

            <button
              @click="nextPage"
              :disabled="pagination.page === pagination.totalPages"
              class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      </Card>

      <!-- Rule Form Modal -->
      <div v-if="showForm" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
        <Card class="w-full max-w-lg">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-800">{{ editingRule ? '编辑规则' : '添加规则' }}</h3>
            <button @click="showForm = false" class="text-gray-500 hover:text-gray-700">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <!-- 错误提示 -->
          <div v-if="errorMessage" class="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
            {{ errorMessage }}
          </div>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">规则名称</label>
              <input
                v-model="ruleForm.name"
                type="text"
                placeholder="输入规则名称"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
              <p class="mt-1 text-xs text-gray-500">规则名称必须唯一，用于标识规则</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">关键字</label>
              <input
                v-model="ruleForm.keywords"
                type="text"
                placeholder="多个关键字用逗号分隔"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
              <p class="mt-1 text-xs text-gray-500">多个关键字用逗号分隔，例如：log4j,spring,apache</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">厂商</label>
              <input
                v-model="ruleForm.vendor_name"
                type="text"
                placeholder="厂商名称"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">产品</label>
              <input
                v-model="ruleForm.product_name"
                type="text"
                placeholder="产品名称"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">最低CVSS评分</label>
              <input
                v-model.number="ruleForm.min_cvss_score"
                type="number"
                min="0"
                max="10"
                step="0.1"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
            </div>

            <div class="flex items-center">
              <input
                v-model="ruleForm.enabled"
                type="checkbox"
                id="rule-enabled"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              >
              <label for="rule-enabled" class="ml-2 block text-sm text-gray-900">启用规则</label>
            </div>
          </div>

          <div class="mt-6 flex justify-end space-x-3">
            <button
              @click="showForm = false"
              class="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
            >
              取消
            </button>
            <button
              @click="editingRule ? updateRule() : createRule()"
              class="btn-primary"
            >
              {{ editingRule ? '更新' : '创建' }}
            </button>
          </div>
        </Card>
      </div>
    </div>
  `
};
