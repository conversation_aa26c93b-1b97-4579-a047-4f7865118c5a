<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>登录 - CVE监控系统</title>
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Vue 3 -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <!-- Axios -->
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <!-- Custom styles -->
  <style>
    body {
      font-family: 'Inter', sans-serif;
      background-color: #f9fafb;
    }
  </style>
</head>
<body>
  <div id="app"></div>

  <script type="module">
    // 使用全局变量
    const { createApp } = Vue;

    // 导入登录组件
    import LoginView from '/js/views/LoginView.js';

    // 创建并挂载应用
    const app = createApp(LoginView);
    app.mount('#app');
  </script>
</body>
</html>
