package utils

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"time"
)

var (
	// OpenCVEBaseURL 是OpenCVE API的基础URL，可以在测试中修改
	OpenCVEBaseURL = "https://app.opencve.io/api"
)

// OpenCVEClient 是OpenCVE API的客户端
type OpenCVEClient struct {
	Username   string
	Password   string
	HTTPClient *http.Client
}

// NewOpenCVEClient 创建一个新的OpenCVE API客户端
func NewOpenCVEClient(username, password string) *OpenCVEClient {
	return &OpenCVEClient{
		Username: username,
		Password: password,
		HTTPClient: &http.Client{
			Timeout: time.Second * 30,
		},
	}
}

// CVEResponse 表示OpenCVE API的响应
type CVEResponse struct {
	Count    int       `json:"count"`
	Next     string    `json:"next"`
	Previous string    `json:"previous"`
	Results  []CVEItem `json:"results"`
}

// CVEItem 表示OpenCVE API中的CVE项
type CVEItem struct {
	CreatedAt   string   `json:"created_at"`
	UpdatedAt   string   `json:"updated_at"`
	CVEID       string   `json:"cve_id"`
	Description string   `json:"description"`
	Title       string   `json:"title,omitempty"`
	Metrics     *Metrics `json:"metrics,omitempty"`
	Weaknesses  []string `json:"weaknesses,omitempty"`
	Vendors     []string `json:"vendors,omitempty"`
}

// Metrics 表示CVE的各种评分指标
type Metrics struct {
	CVSSV31 *CVSSMetric `json:"cvssV3_1,omitempty"`
}

// CVSSMetric 表示CVSS评分信息
type CVSSMetric struct {
	Data     *CVSSData `json:"data,omitempty"`
	Provider string    `json:"provider,omitempty"`
}

// CVSSData 表示CVSS评分数据
type CVSSData struct {
	Score  float64 `json:"score,omitempty"`
	Vector string  `json:"vector,omitempty"`
}

// GetCVEs 从 OpenCVE API 获取CVE
func (c *OpenCVEClient) GetCVEs(since time.Time) ([]CVEItem, error) {
	// 默认不指定CVSS级别和最大年龄
	return c.GetCVEsByCVSS(since, "", 0)
}

// GetCVEsByCVSS 根据CVSS级别从 OpenCVE API 获取CVE
func (c *OpenCVEClient) GetCVEsByCVSS(since time.Time, cvssLevel string, maxAge int) ([]CVEItem, error) {
	// 获取CVE列表
	cveList, err := c.GetCVEListByCVSS(since, cvssLevel, maxAge)
	if err != nil {
		return nil, err
	}

	// 如果没有CVE，直接返回
	if len(cveList) == 0 {
		return []CVEItem{}, nil
	}

	// 获取每个CVE的详细信息
	detailedCVEs := []CVEItem{}
	for _, cve := range cveList {
		detail, err := c.GetCVEDetail(cve.CVEID)
		if err != nil {
			log.Printf("获取CVE %s 的详细信息失败: %v", cve.CVEID, err)
			continue
		}
		detailedCVEs = append(detailedCVEs, detail)
	}

	return detailedCVEs, nil
}

// GetCVEsByCVSSOptimized 优化版本：先检查数据库，再获取详情
func (c *OpenCVEClient) GetCVEsByCVSSOptimized(since time.Time, cvssLevel string, maxAge int, existingCVEChecker func(string) bool) ([]CVEItem, error) {
	// 获取CVE列表
	cveList, err := c.GetCVEListByCVSS(since, cvssLevel, maxAge)
	if err != nil {
		return nil, err
	}

	// 如果没有CVE，直接返回
	if len(cveList) == 0 {
		return []CVEItem{}, nil
	}

	// 过滤出不在数据库中的CVE
	newCVEList := []CVEItem{}
	for _, cve := range cveList {
		if !existingCVEChecker(cve.CVEID) {
			newCVEList = append(newCVEList, cve)
		} else {
			log.Printf("CVE %s 已存在于数据库中，跳过获取详情", cve.CVEID)
		}
	}

	// 如果没有新CVE，直接返回
	if len(newCVEList) == 0 {
		log.Printf("所有CVE都已存在于数据库中，无需获取详情")
		return []CVEItem{}, nil
	}

	log.Printf("需要获取详情的CVE数量: %d / %d", len(newCVEList), len(cveList))

	// 只为新CVE获取详细信息
	detailedCVEs := []CVEItem{}
	for _, cve := range newCVEList {
		detail, err := c.GetCVEDetail(cve.CVEID)
		if err != nil {
			log.Printf("获取CVE %s 的详细信息失败: %v", cve.CVEID, err)
			continue
		}
		detailedCVEs = append(detailedCVEs, detail)
	}

	return detailedCVEs, nil
}

// GetCVEDetail 获取单个CVE的详细信息
func (c *OpenCVEClient) GetCVEDetail(cveID string) (CVEItem, error) {
	// 构建请求URL
	url := fmt.Sprintf("%s/cve/%s", OpenCVEBaseURL, cveID)
	log.Printf("获取CVE详情: %s", url)

	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return CVEItem{}, err
	}

	// 添加基本认证
	if c.Username != "" && c.Password != "" {
		req.SetBasicAuth(c.Username, c.Password)
	}
	req.Header.Add("Accept", "application/json")

	// 发送请求
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return CVEItem{}, err
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		switch resp.StatusCode {
		case 403:
			return CVEItem{}, fmt.Errorf("获取CVE详情失败 - OpenCVE API 访问被拒绝 (403) - 可能是速率限制或认证问题")
		case 429:
			return CVEItem{}, fmt.Errorf("获取CVE详情失败 - OpenCVE API 速率限制 (429) - 请求过于频繁")
		case 401:
			return CVEItem{}, fmt.Errorf("获取CVE详情失败 - OpenCVE API 认证失败 (401) - 请检查用户名和密码")
		case 404:
			return CVEItem{}, fmt.Errorf("获取CVE详情失败 - CVE不存在 (404)")
		case 500:
			return CVEItem{}, fmt.Errorf("获取CVE详情失败 - OpenCVE API 服务器内部错误 (500)")
		default:
			return CVEItem{}, fmt.Errorf("获取CVE详情失败，状态码: %d", resp.StatusCode)
		}
	}

	// 解析响应
	var cveDetail CVEItem
	if err := json.NewDecoder(resp.Body).Decode(&cveDetail); err != nil {
		return CVEItem{}, err
	}

	return cveDetail, nil
}

// GetCVEListByCVSS 根据CVSS级别从 OpenCVE API 获取CVE列表
func (c *OpenCVEClient) GetCVEListByCVSS(since time.Time, cvssLevel string, maxAge int) ([]CVEItem, error) {
	// 按API要求格式化时间
	sinceStr := since.Format("2006-01-02T15:04:05")
	log.Printf("获取自 %s 以来的CVE数据", sinceStr)

	// 构建带查询参数的URL
	baseURL := fmt.Sprintf("%s/cve", OpenCVEBaseURL)

	// 添加查询参数
	queryURL, err := url.Parse(baseURL)
	if err != nil {
		return nil, err
	}

	// 添加查询参数
	query := queryURL.Query()

	// 注意: /api/cve 接口不支持 updated_at 参数
	// 我们将在客户端对返回的结果进行时间过滤

	// 如果指定了CVSS级别，添加CVSS参数
	if cvssLevel != "" {
		log.Printf("按CVSS级别 %s 过滤CVE数据", cvssLevel)
		query.Add("cvss", cvssLevel)
	}

	queryURL.RawQuery = query.Encode()

	// 创建请求
	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	// 添加基本认证
	if c.Username != "" && c.Password != "" {
		req.SetBasicAuth(c.Username, c.Password)
	}
	req.Header.Add("Accept", "application/json")

	// 发送请求
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		switch resp.StatusCode {
		case 403:
			return nil, fmt.Errorf("OpenCVE API 访问被拒绝 (403) - 可能是速率限制或认证问题")
		case 429:
			return nil, fmt.Errorf("OpenCVE API 速率限制 (429) - 请求过于频繁")
		case 401:
			return nil, fmt.Errorf("OpenCVE API 认证失败 (401) - 请检查用户名和密码")
		case 500:
			return nil, fmt.Errorf("OpenCVE API 服务器内部错误 (500)")
		default:
			return nil, fmt.Errorf("OpenCVE API 返回状态码 %d", resp.StatusCode)
		}
	}

	// 解析响应
	var cveResp CVEResponse
	if err := json.NewDecoder(resp.Body).Decode(&cveResp); err != nil {
		return nil, err
	}

	// 在客户端进行时间过滤
	filteredResults := []CVEItem{}
	for _, item := range cveResp.Results {
		// 解析CVE的更新时间
		updatedAt, err := time.Parse(time.RFC3339, item.UpdatedAt)
		if err != nil {
			log.Printf("解析CVE更新时间失败: %v, 使用原始时间字符串: %s", err, item.UpdatedAt)
			continue
		}

		// 解析CVE的创建时间
		createdAt, err := time.Parse(time.RFC3339, item.CreatedAt)
		if err != nil {
			log.Printf("解析CVE创建时间失败: %v, 使用原始时间字符串: %s", err, item.CreatedAt)
			continue
		}

		// 只保留在since之后更新的CVE
		if !updatedAt.After(since) {
			log.Printf("CVE %s 的更新时间 %s 不在指定时间 %s 之后，跳过",
				item.CVEID, updatedAt.Format(time.RFC3339), since.Format(time.RFC3339))
			continue
		}

		// 如果设置了最大年龄，检查CVE的创建时间
		if maxAge > 0 {
			// 计算CVE的年龄（天）
			now := time.Now()
			ageInDays := now.Sub(createdAt).Hours() / 24

			// 如果CVE的年龄超过了最大年龄，跳过
			if ageInDays > float64(maxAge) {
				log.Printf("CVE %s 的创建时间为 %s，年龄为 %.1f 天，超过了最大年龄 %d 天，跳过",
					item.CVEID, createdAt.Format(time.RFC3339), ageInDays, maxAge)
				continue
			}

			log.Printf("CVE %s 的创建时间为 %s，年龄为 %.1f 天，在最大年龄 %d 天范围内",
				item.CVEID, createdAt.Format(time.RFC3339), ageInDays, maxAge)
		}

		// 通过所有检查，保留该CVE
		log.Printf("CVE %s 的更新时间 %s 在指定时间 %s 之后，保留",
			item.CVEID, updatedAt.Format(time.RFC3339), since.Format(time.RFC3339))
		filteredResults = append(filteredResults, item)
	}

	// 如果第一页没有符合条件的结果，直接返回
	if len(filteredResults) == 0 {
		log.Printf("没有找到在 %s 之后更新的CVE", since.Format(time.RFC3339))
		return filteredResults, nil
	}

	// 获取后续页面
	allResults := filteredResults
	nextURL := cveResp.Next
	pageCount := 1
	maxResults := 1000 // 最多获取1000条数据

	// 检查当前页的最后一条CVE的更新时间
	// 如果最后一条CVE的更新时间不在since之后，则不需要继续获取后续页面
	needNextPage := false
	if len(cveResp.Results) > 0 {
		lastItem := cveResp.Results[len(cveResp.Results)-1]
		lastUpdatedAt, err := time.Parse(time.RFC3339, lastItem.UpdatedAt)
		if err != nil {
			log.Printf("解析最后一条CVE的更新时间失败: %v", err)
		} else {
			// 将两个时间转换为 UTC 时间再进行比较
			lastUpdatedAtUTC := lastUpdatedAt.UTC()
			sinceUTC := since.UTC()
			needNextPage = lastUpdatedAtUTC.After(sinceUTC)
			log.Printf("当前页的最后一条CVE %s 的更新时间是 %s (UTC: %s)，上次运行时间是 %s (UTC: %s)，是否需要获取下一页: %v",
				lastItem.CVEID, lastUpdatedAt.Format(time.RFC3339), lastUpdatedAtUTC.Format(time.RFC3339),
				since.Format(time.RFC3339), sinceUTC.Format(time.RFC3339), needNextPage)
		}
	}

	for nextURL != "" && len(allResults) < maxResults && needNextPage {
		pageCount++
		log.Printf("获取CVE数据第%d页", pageCount)

		req, err := http.NewRequest("GET", nextURL, nil)
		if err != nil {
			return nil, err
		}

		// 添加基本认证
		if c.Username != "" && c.Password != "" {
			req.SetBasicAuth(c.Username, c.Password)
		}
		req.Header.Add("Accept", "application/json")

		resp, err := c.HTTPClient.Do(req)
		if err != nil {
			return nil, err
		}

		// 检查响应状态码
		if resp.StatusCode != http.StatusOK {
			resp.Body.Close()
			switch resp.StatusCode {
			case 403:
				return nil, fmt.Errorf("获取下一页数据失败 - OpenCVE API 访问被拒绝 (403) - 可能是速率限制或认证问题")
			case 429:
				return nil, fmt.Errorf("获取下一页数据失败 - OpenCVE API 速率限制 (429) - 请求过于频繁")
			case 401:
				return nil, fmt.Errorf("获取下一页数据失败 - OpenCVE API 认证失败 (401) - 请检查用户名和密码")
			case 500:
				return nil, fmt.Errorf("获取下一页数据失败 - OpenCVE API 服务器内部错误 (500)")
			default:
				return nil, fmt.Errorf("获取下一页数据失败，状态码: %d", resp.StatusCode)
			}
		}

		var nextResp CVEResponse
		if err := json.NewDecoder(resp.Body).Decode(&nextResp); err != nil {
			resp.Body.Close()
			return nil, err
		}
		resp.Body.Close()

		// 检查是否有新的结果
		if len(nextResp.Results) == 0 {
			break
		}

		// 在客户端进行时间过滤
		filteredNextResults := []CVEItem{}
		for _, item := range nextResp.Results {
			// 解析CVE的更新时间
			updatedAt, err := time.Parse(time.RFC3339, item.UpdatedAt)
			if err != nil {
				log.Printf("解析CVE更新时间失败: %v, 使用原始时间字符串: %s", err, item.UpdatedAt)
				continue
			}

			// 解析CVE的创建时间
			createdAt, err := time.Parse(time.RFC3339, item.CreatedAt)
			if err != nil {
				log.Printf("解析CVE创建时间失败: %v, 使用原始时间字符串: %s", err, item.CreatedAt)
				continue
			}

			// 只保留在since之后更新的CVE
			if !updatedAt.After(since) {
				log.Printf("CVE %s 的更新时间 %s 不在指定时间 %s 之后，跳过",
					item.CVEID, updatedAt.Format(time.RFC3339), since.Format(time.RFC3339))
				continue
			}

			// 如果设置了最大年龄，检查CVE的创建时间
			if maxAge > 0 {
				// 计算CVE的年龄（天）
				now := time.Now()
				ageInDays := now.Sub(createdAt).Hours() / 24

				// 如果CVE的年龄超过了最大年龄，跳过
				if ageInDays > float64(maxAge) {
					log.Printf("CVE %s 的创建时间为 %s，年龄为 %.1f 天，超过了最大年龄 %d 天，跳过",
						item.CVEID, createdAt.Format(time.RFC3339), ageInDays, maxAge)
					continue
				}

				log.Printf("CVE %s 的创建时间为 %s，年龄为 %.1f 天，在最大年龄 %d 天范围内",
					item.CVEID, createdAt.Format(time.RFC3339), ageInDays, maxAge)
			}

			// 通过所有检查，保留该CVE
			log.Printf("CVE %s 的更新时间 %s 在指定时间 %s 之后，保留",
				item.CVEID, updatedAt.Format(time.RFC3339), since.Format(time.RFC3339))
			filteredNextResults = append(filteredNextResults, item)
		}

		// 如果该页没有符合条件的结果，停止获取后续页面
		if len(filteredNextResults) == 0 {
			log.Printf("第%d页没有找到在 %s 之后更新的CVE，停止获取", pageCount, since.Format(time.RFC3339))
			break
		}

		// 检查当前页的最后一条CVE的更新时间
		// 如果最后一条CVE的更新时间不在since之后，则不需要继续获取后续页面
		needNextPage = false
		if len(nextResp.Results) > 0 {
			lastItem := nextResp.Results[len(nextResp.Results)-1]
			lastUpdatedAt, err := time.Parse(time.RFC3339, lastItem.UpdatedAt)
			if err != nil {
				log.Printf("解析最后一条CVE的更新时间失败: %v", err)
			} else {
				// 将两个时间转换为 UTC 时间再进行比较
				lastUpdatedAtUTC := lastUpdatedAt.UTC()
				sinceUTC := since.UTC()
				needNextPage = lastUpdatedAtUTC.After(sinceUTC)
				log.Printf("第%d页的最后一条CVE %s 的更新时间是 %s (UTC: %s)，上次运行时间是 %s (UTC: %s)，是否需要获取下一页: %v",
					pageCount, lastItem.CVEID, lastUpdatedAt.Format(time.RFC3339), lastUpdatedAtUTC.Format(time.RFC3339),
					since.Format(time.RFC3339), sinceUTC.Format(time.RFC3339), needNextPage)
			}
		}

		allResults = append(allResults, filteredNextResults...)
		nextURL = nextResp.Next
	}

	log.Printf("共获取了%d页CVE数据，筛选后总计%d条在 %s 之后更新的CVE", pageCount, len(allResults), since.Format(time.RFC3339))

	return allResults, nil
}
